<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Midjourney Prompt Manager v2.0 - Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .test-section h3 {
            margin-top: 0;
            color: #4a5568;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        .instructions {
            background: #e8f5e8;
            border: 1px solid #c8e6c9;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
        }
        .test-prompts {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-line;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Midjourney Prompt Manager v2.0</h1>
        <h2>Extension Testing & Validation Guide</h2>

        <div class="test-section">
            <h3>🚀 New Features Implemented</h3>
            <ul class="feature-list">
                <li>Batch submission system (7-10 prompts per batch)</li>
                <li>Randomized timing (16-40 min between batches, 1-10s between prompts)</li>
                <li>Prompt list management with file save/load</li>
                <li>Thumbnail support for prompt lists</li>
                <li>Advanced configuration options</li>
                <li>Comprehensive error handling and logging</li>
                <li>Support for both Midjourney.com and Discord.com</li>
                <li>Enhanced UI with modern design</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📋 Installation Instructions</h3>
            <div class="instructions">
                <ol>
                    <li>Open Chrome and navigate to <code>chrome://extensions/</code></li>
                    <li>Enable "Developer mode" in the top right corner</li>
                    <li>Click "Load unpacked" and select the extension folder</li>
                    <li>The extension icon should appear in the toolbar</li>
                    <li>Pin the extension for easy access</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Testing Checklist</h3>
            
            <h4>1. Basic Functionality</h4>
            <ul>
                <li>✅ Extension loads without errors</li>
                <li>✅ Popup opens and displays correctly</li>
                <li>✅ Can enter prompts in the text area</li>
                <li>✅ Manual prompt posting works</li>
            </ul>

            <h4>2. Prompt List Management</h4>
            <ul>
                <li>✅ Can create new prompt lists</li>
                <li>✅ Can save lists with names and descriptions</li>
                <li>✅ Can load existing lists from dropdown</li>
                <li>✅ Can delete prompt lists</li>
                <li>✅ Can export lists as JSON files</li>
                <li>✅ Can import lists from JSON files</li>
            </ul>

            <h4>3. Batch Processing</h4>
            <ul>
                <li>✅ Batch mode toggle works</li>
                <li>✅ Random batch sizes (7-10 prompts)</li>
                <li>✅ Random delays between batches (16-40 min)</li>
                <li>✅ Random delays between prompts (1-10 sec)</li>
                <li>✅ Status updates show progress</li>
                <li>✅ Can stop batch processing</li>
            </ul>

            <h4>4. Advanced Settings</h4>
            <ul>
                <li>✅ Settings modal opens</li>
                <li>✅ Can configure timing parameters</li>
                <li>✅ Settings are saved and restored</li>
                <li>✅ Validation prevents invalid settings</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📝 Sample Test Prompts</h3>
            <div class="test-prompts">a beautiful sunset over mountains --ar 16:9
abstract digital art with vibrant colors --v 6
portrait of a wise old wizard --style raw
futuristic cityscape at night --ar 21:9
cute cartoon cat playing with yarn --niji 5
minimalist logo design for tech company
vintage poster art style illustration
photorealistic food photography --ar 4:5
fantasy dragon in mystical forest
modern architecture building design</div>
        </div>

        <div class="test-section">
            <h3>⚠️ Important Notes</h3>
            <div class="warning">
                <ul>
                    <li><strong>Rate Limiting:</strong> The extension includes built-in delays to prevent overwhelming the Midjourney service</li>
                    <li><strong>Tab Requirements:</strong> Must have an active Midjourney.com or Discord.com tab open</li>
                    <li><strong>Permissions:</strong> Extension requires storage, downloads, and notifications permissions</li>
                    <li><strong>Data Storage:</strong> All prompt lists are stored locally in Chrome storage</li>
                    <li><strong>Backup:</strong> Export your prompt lists regularly as backup</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Troubleshooting</h3>
            <h4>Common Issues:</h4>
            <ul>
                <li><strong>Prompts not submitting:</strong> Check if you're on the correct website (Midjourney.com or Discord.com)</li>
                <li><strong>Extension not loading:</strong> Check Chrome developer console for errors</li>
                <li><strong>Batch processing stops:</strong> Check error log in advanced settings</li>
                <li><strong>File import fails:</strong> Ensure JSON file format is correct</li>
            </ul>

            <h4>Debug Information:</h4>
            <div class="code">
                Check Chrome DevTools Console (F12) for detailed logs when logging is enabled.
                Error logs are stored in extension storage and can be viewed in advanced settings.
            </div>
        </div>

        <div class="test-section">
            <h3>📊 Performance Considerations</h3>
            <ul>
                <li>Extension uses minimal memory and CPU resources</li>
                <li>Batch processing runs in background service worker</li>
                <li>Error logs are limited to 100 entries to prevent storage bloat</li>
                <li>Thumbnails are compressed to reduce storage usage</li>
                <li>All operations are asynchronous to prevent UI blocking</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎯 Success Criteria</h3>
            <div class="instructions">
                <p><strong>The extension is working correctly if:</strong></p>
                <ul>
                    <li>All UI elements load and function properly</li>
                    <li>Prompt lists can be created, saved, and loaded</li>
                    <li>Batch processing starts and maintains random timing</li>
                    <li>Manual prompt posting works on target websites</li>
                    <li>Settings can be configured and persist</li>
                    <li>Error handling provides meaningful feedback</li>
                    <li>File import/export functions correctly</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
