<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icons</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .icon-container { 
            display: inline-block; 
            margin: 20px; 
            text-align: center; 
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas { 
            border: 2px solid #ddd; 
            border-radius: 8px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        h1 { text-align: center; color: #333; }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>Extension Icon Generator</h1>
    
    <div class="instructions">
        <h3>Instructions:</h3>
        <ol>
            <li>Click "Generate Icons" to create all three sizes</li>
            <li>Right-click each icon and "Save image as..."</li>
            <li>Save them as: <code>icon16.png</code>, <code>icon48.png</code>, <code>icon128.png</code></li>
            <li>Put the PNG files in your extension folder: <code>D:\crhomeext10x\</code></li>
            <li>Update manifest.json to include the icons</li>
        </ol>
    </div>
    
    <div style="text-align: center;">
        <button onclick="generateAllIcons()" style="font-size: 16px; padding: 15px 30px;">Generate Icons</button>
        <button onclick="downloadAllIcons()" style="font-size: 16px; padding: 15px 30px;">Download All Icons</button>
    </div>
    
    <div id="iconsContainer"></div>
    
    <div class="instructions">
        <h3>After creating icons, update your manifest.json:</h3>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
{
  "manifest_version": 3,
  "name": "Midjourney Prompt Manager",
  "version": "2.0",
  "description": "Advanced batch prompt management and submission for Midjourney",
  "permissions": [
    "storage",
    "activeTab",
    "scripting",
    "downloads",
    "unlimitedStorage",
    "notifications"
  ],
  "host_permissions": [
    "*://www.midjourney.com/*",
    "*://discord.com/*"
  ],
  "action": {
    "default_popup": "popup.html",
    "default_icon": {
      "16": "icon16.png",
      "48": "icon48.png",
      "128": "icon128.png"
    }
  },
  "icons": {
    "16": "icon16.png",
    "48": "icon48.png",
    "128": "icon128.png"
  },
  "content_scripts": [
    {
      "matches": ["*://www.midjourney.com/*", "*://discord.com/*"],
      "js": ["content.js"]
    }
  ],
  "background": {
    "service_worker": "background.js"
  }
}
        </pre>
    </div>

    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add rounded corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            const radius = size * 0.15;
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Add "MJ" text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('MJ', size/2, size/2);
            
            // Add small "v2" indicator
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            ctx.font = `${size * 0.15}px Arial`;
            ctx.fillText('v2', size * 0.8, size * 0.9);
            
            return canvas;
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function generateAllIcons() {
            const container = document.getElementById('iconsContainer');
            container.innerHTML = '';
            
            const sizes = [16, 48, 128];
            
            sizes.forEach(size => {
                const iconContainer = document.createElement('div');
                iconContainer.className = 'icon-container';
                
                const title = document.createElement('h3');
                title.textContent = `${size}x${size} Icon`;
                
                const canvas = createIcon(size);
                canvas.style.width = Math.max(size, 64) + 'px';
                canvas.style.height = Math.max(size, 64) + 'px';
                canvas.style.imageRendering = size <= 16 ? 'pixelated' : 'auto';
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = `Download icon${size}.png`;
                downloadBtn.onclick = () => downloadCanvas(canvas, `icon${size}.png`);
                
                const saveBtn = document.createElement('button');
                saveBtn.textContent = 'Right-click to Save';
                saveBtn.style.background = '#28a745';
                saveBtn.onclick = () => alert('Right-click on the icon above and select "Save image as..."');
                
                iconContainer.appendChild(title);
                iconContainer.appendChild(canvas);
                iconContainer.appendChild(document.createElement('br'));
                iconContainer.appendChild(downloadBtn);
                iconContainer.appendChild(saveBtn);
                
                container.appendChild(iconContainer);
            });
        }
        
        function downloadAllIcons() {
            const sizes = [16, 48, 128];
            sizes.forEach(size => {
                const canvas = createIcon(size);
                setTimeout(() => {
                    downloadCanvas(canvas, `icon${size}.png`);
                }, size * 10); // Stagger downloads
            });
        }
        
        // Auto-generate on page load
        window.addEventListener('load', generateAllIcons);
    </script>
</body>
</html>
