// Enhanced content script for Midjourney Prompt Manager v2.0
// Supports both Midjourney.com and Discord.com
// Cross-browser compatible with fallback mechanisms

// Prevent multiple script injections
if (window.midjourneyPromptManagerLoaded) {
    console.log('Midjourney Prompt Manager content script already loaded, skipping...');
    // Exit early to prevent redeclaration
} else {
    window.midjourneyPromptManagerLoaded = true;

    class PromptSubmitter {
    constructor() {
        this.isDiscord = window.location.hostname.includes('discord.com');
        this.isMidjourney = window.location.hostname.includes('midjourney.com');
        this.lastSubmissionTime = 0;
        this.minSubmissionInterval = 1000; // Minimum 1 second between submissions
        this.browserInfo = this.detectBrowser();
        this.retryAttempts = 0;
        this.maxRetries = 3;
    }

    // Detect browser for compatibility adjustments
    detectBrowser() {
        const userAgent = navigator.userAgent;
        return {
            isChrome: userAgent.includes('Chrome') && !userAgent.includes('Edg'),
            isEdge: userAgent.includes('Edg'),
            isFirefox: userAgent.includes('Firefox'),
            version: this.extractVersion(userAgent)
        };
    }

    // Extract browser version
    extractVersion(userAgent) {
        const match = userAgent.match(/Chrome\/(\d+)/);
        return match ? parseInt(match[1]) : 0;
    }

    // Find the appropriate input field with cross-browser compatibility
    findPromptInput() {
        const selectors = this.getInputSelectors();

        for (const selector of selectors) {
            try {
                const element = document.querySelector(selector);
                if (element && this.isElementVisible(element)) {
                    return element;
                }
            } catch (error) {
                console.warn(`Selector failed: ${selector}`, error);
            }
        }

        // Fallback: search for any input-like element
        return this.findFallbackInput();
    }

    // Get platform-specific selectors
    getInputSelectors() {
        if (this.isMidjourney) {
            return [
                '#desktop_input_bar',
                'input[placeholder*="imagine" i]',
                'input[placeholder*="prompt" i]',
                'textarea[placeholder*="imagine" i]',
                'textarea[placeholder*="prompt" i]',
                'input[type="text"]',
                'textarea'
            ];
        } else if (this.isDiscord) {
            return [
                'div[role="textbox"][data-slate-editor="true"]',
                'div[contenteditable="true"][data-slate-editor="true"]',
                'div[aria-label*="Message" i]',
                'div[class*="textArea"]',
                'div[class*="messageInput"]',
                'div[contenteditable="true"]',
                '[data-slate-editor="true"]'
            ];
        }
        return [];
    }

    // Check if element is visible and interactable
    isElementVisible(element) {
        if (!element) return false;

        const style = window.getComputedStyle(element);
        return style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               style.opacity !== '0' &&
               element.offsetWidth > 0 &&
               element.offsetHeight > 0;
    }

    // Fallback input finder
    findFallbackInput() {
        const inputs = document.querySelectorAll('input[type="text"], textarea, div[contenteditable="true"]');
        for (const input of inputs) {
            if (this.isElementVisible(input)) {
                return input;
            }
        }
        return null;
    }

    // Find the submit button based on the platform
    findSubmitButton() {
        if (this.isMidjourney) {
            return document.querySelector('button[type="submit"]') ||
                   document.querySelector('button[aria-label*="generate"]') ||
                   document.querySelector('button[aria-label*="submit"]') ||
                   Array.from(document.querySelectorAll('button')).find(btn =>
                       btn.textContent.toLowerCase().includes('imagine') ||
                       btn.textContent.toLowerCase().includes('generate') ||
                       btn.textContent.toLowerCase().includes('submit')
                   );
        } else if (this.isDiscord) {
            return document.querySelector('button[aria-label*="Send message"]') ||
                   document.querySelector('button[type="submit"]') ||
                   document.querySelector('div[role="button"][aria-label*="Send"]') ||
                   Array.from(document.querySelectorAll('button')).find(btn =>
                       btn.getAttribute('aria-label')?.toLowerCase().includes('send')
                   );
        }
        return null;
    }

    // Set text in the input field
    setInputText(input, text) {
        if (!input) return false;

        try {
            // For Discord's contenteditable div
            if (input.contentEditable === 'true') {
                input.focus();

                // Clear existing content
                input.innerHTML = '';

                // Set new content
                input.textContent = text;

                // Trigger input events
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));

                // For Slate.js (Discord's editor)
                const slateEvent = new Event('beforeinput', { bubbles: true });
                slateEvent.inputType = 'insertText';
                slateEvent.data = text;
                input.dispatchEvent(slateEvent);

                return true;
            }
            // For regular input/textarea elements
            else if (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA') {
                input.focus();
                input.value = text;

                // Trigger events
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));

                return true;
            }
        } catch (error) {
            console.error('Error setting input text:', error);
        }

        return false;
    }

    // Submit the prompt
    async submitPrompt(prompt) {
        // Rate limiting
        const now = Date.now();
        if (now - this.lastSubmissionTime < this.minSubmissionInterval) {
            console.log('Rate limiting: waiting before submission');
            await new Promise(resolve => setTimeout(resolve, this.minSubmissionInterval));
        }

        const input = this.findPromptInput();
        if (!input) {
            console.error('Could not find prompt input field');
            return false;
        }

        console.log(`Submitting prompt on ${this.isDiscord ? 'Discord' : 'Midjourney'}: ${prompt.substring(0, 50)}...`);

        // Set the prompt text
        if (!this.setInputText(input, prompt)) {
            console.error('Failed to set input text');
            return false;
        }

        // Wait a moment for the text to be processed
        await new Promise(resolve => setTimeout(resolve, 200));

        // Try to submit via Enter key first
        const enterSuccess = await this.submitViaEnter(input);
        if (enterSuccess) {
            this.lastSubmissionTime = Date.now();
            return true;
        }

        // Fallback to button click
        const buttonSuccess = await this.submitViaButton();
        if (buttonSuccess) {
            this.lastSubmissionTime = Date.now();
            return true;
        }

        console.error('Failed to submit prompt');
        return false;
    }

    // Submit via Enter key with multiple attempts
    async submitViaEnter(input) {
        try {
            // Try multiple enter key events for better compatibility
            const events = [
                new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true,
                    cancelable: true
                }),
                new KeyboardEvent('keypress', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true,
                    cancelable: true
                }),
                new KeyboardEvent('keyup', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true,
                    cancelable: true
                })
            ];

            // Dispatch all enter events
            events.forEach(event => input.dispatchEvent(event));

            // Wait and check if submission was successful
            await new Promise(resolve => setTimeout(resolve, 500));

            // Check if the input was cleared (indication of successful submission)
            const currentValue = input.value || input.textContent || '';
            const wasCleared = currentValue.trim() === '';

            if (!wasCleared) {
                // Try form submission if enter didn't work
                const form = input.closest('form');
                if (form) {
                    form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
                    await new Promise(resolve => setTimeout(resolve, 300));
                    const newValue = input.value || input.textContent || '';
                    return newValue.trim() === '';
                }
            }

            return wasCleared;
        } catch (error) {
            console.error('Error submitting via Enter:', error);
            return false;
        }
    }

    // Submit via button click
    async submitViaButton() {
        try {
            const button = this.findSubmitButton();
            if (!button) {
                console.error('Could not find submit button');
                return false;
            }

            button.click();

            // Wait and verify submission
            await new Promise(resolve => setTimeout(resolve, 300));
            return true;
        } catch (error) {
            console.error('Error submitting via button:', error);
            return false;
        }
    }
}



    } // End of PromptSubmitter class

    // Initialize the prompt submitter
    const promptSubmitter = new PromptSubmitter();

    // Message listener with better error handling
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'postPrompt') {
            // Handle async operation properly
            (async () => {
                try {
                    console.log('Received prompt submission request:', request.prompt.substring(0, 50) + '...');
                    const success = await promptSubmitter.submitPrompt(request.prompt);
                    sendResponse({ success, message: success ? 'Prompt submitted' : 'Submission failed' });
                } catch (error) {
                    console.error('Error in postPrompt:', error);
                    sendResponse({ success: false, error: error.message });
                }
            })();

            return true; // Keep the message channel open for async response
        } else if (request.action === 'ping') {
            // Respond to ping requests
            sendResponse({ success: true, message: 'Content script is active' });
            return true;
        }
    });

    // Log when the content script loads
    console.log(`Midjourney Prompt Manager v2.0 content script loaded on ${window.location.hostname}`);

    // Notify that script is ready
    try {
        chrome.runtime.sendMessage({ action: 'contentScriptReady', hostname: window.location.hostname });
    } catch (error) {
        console.log('Could not notify background script:', error.message);
    }

} // End of duplicate prevention check
