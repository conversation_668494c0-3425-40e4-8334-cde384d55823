// Enhanced content script for Midjourney Prompt Manager v2.0
// Supports both Midjourney.com and Discord.com
// Cross-browser compatible with fallback mechanisms

console.log('Midjourney Prompt Manager content script starting...');

// Prevent multiple script injections with more robust checking
if (window.midjourneyPromptManagerLoaded && window.midjourneyPromptManagerVersion === '2.0') {
    console.log('Midjourney Prompt Manager content script already loaded, skipping...');
    // Still respond to ping requests even if already loaded
    if (chrome && chrome.runtime && chrome.runtime.onMessage) {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'ping') {
                sendResponse({ success: true, message: 'Content script is active (already loaded)' });
                return true;
            }
        });
    }
} else {
    window.midjourneyPromptManagerLoaded = true;
    window.midjourneyPromptManagerVersion = '2.0';

    class PromptSubmitter {
    constructor() {
        this.isDiscord = window.location.hostname.includes('discord.com');
        this.isMidjourney = window.location.hostname.includes('midjourney.com');
        this.lastSubmissionTime = 0;
        this.minSubmissionInterval = 1000; // Minimum 1 second between submissions
        this.browserInfo = this.detectBrowser();
        this.retryAttempts = 0;
        this.maxRetries = 3;
    }

    // Detect browser for compatibility adjustments
    detectBrowser() {
        const userAgent = navigator.userAgent;
        return {
            isChrome: userAgent.includes('Chrome') && !userAgent.includes('Edg'),
            isEdge: userAgent.includes('Edg'),
            isFirefox: userAgent.includes('Firefox'),
            version: this.extractVersion(userAgent)
        };
    }

    // Extract browser version
    extractVersion(userAgent) {
        const match = userAgent.match(/Chrome\/(\d+)/);
        return match ? parseInt(match[1]) : 0;
    }

    // Find the appropriate input field with cross-browser compatibility
    findPromptInput() {
        const selectors = this.getInputSelectors();

        for (const selector of selectors) {
            try {
                const element = document.querySelector(selector);
                if (element && this.isElementVisible(element)) {
                    return element;
                }
            } catch (error) {
                console.warn(`Selector failed: ${selector}`, error);
            }
        }

        // Fallback: search for any input-like element
        return this.findFallbackInput();
    }

    // Midjourney-specific selectors (prioritized for safety)
    getInputSelectors() {
        if (this.isMidjourney) {
            return [
                // Primary Midjourney selectors (most reliable)
                '#desktop_input_bar',
                'input[placeholder*="Imagine" i]',
                'input[placeholder*="imagine" i]',
                'textarea[placeholder*="Imagine" i]',
                'textarea[placeholder*="imagine" i]',
                // Fallback selectors
                'input[type="text"]:not([readonly]):not([disabled])',
                'textarea:not([readonly]):not([disabled])'
            ];
        } else if (this.isDiscord) {
            return [
                'div[role="textbox"][data-slate-editor="true"]',
                'div[contenteditable="true"][data-slate-editor="true"]',
                'div[aria-label*="Message" i]',
                'div[class*="textArea"]',
                'div[class*="messageInput"]',
                'div[contenteditable="true"]',
                '[data-slate-editor="true"]'
            ];
        }
        return [];
    }

    // Check if element is visible and interactable
    isElementVisible(element) {
        if (!element) return false;

        const style = window.getComputedStyle(element);
        return style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               style.opacity !== '0' &&
               element.offsetWidth > 0 &&
               element.offsetHeight > 0;
    }

    // Fallback input finder
    findFallbackInput() {
        const inputs = document.querySelectorAll('input[type="text"], textarea, div[contenteditable="true"]');
        for (const input of inputs) {
            if (this.isElementVisible(input)) {
                return input;
            }
        }
        return null;
    }

    // Find the submit button based on the platform
    findSubmitButton() {
        if (this.isMidjourney) {
            return document.querySelector('button[type="submit"]') ||
                   document.querySelector('button[aria-label*="generate"]') ||
                   document.querySelector('button[aria-label*="submit"]') ||
                   Array.from(document.querySelectorAll('button')).find(btn =>
                       btn.textContent.toLowerCase().includes('imagine') ||
                       btn.textContent.toLowerCase().includes('generate') ||
                       btn.textContent.toLowerCase().includes('submit')
                   );
        } else if (this.isDiscord) {
            return document.querySelector('button[aria-label*="Send message"]') ||
                   document.querySelector('button[type="submit"]') ||
                   document.querySelector('div[role="button"][aria-label*="Send"]') ||
                   Array.from(document.querySelectorAll('button')).find(btn =>
                       btn.getAttribute('aria-label')?.toLowerCase().includes('send')
                   );
        }
        return null;
    }

    // Set text in input field with human-like behavior
    setInputText(input, text) {
        if (!input) return false;

        try {
            // Focus the input (like a human clicking)
            input.focus();

            // Add small delay to mimic human behavior
            setTimeout(() => {
                if (input.contentEditable === 'true') {
                    // For contenteditable elements (Discord)
                    input.innerHTML = '';
                    input.textContent = text;

                    // Trigger events that mimic typing
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true }));

                    // For Slate.js (Discord's editor)
                    const slateEvent = new Event('beforeinput', { bubbles: true });
                    slateEvent.inputType = 'insertText';
                    slateEvent.data = text;
                    input.dispatchEvent(slateEvent);
                }
                else if (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA') {
                    // For regular input/textarea (Midjourney)

                    // Clear existing content first (like human selecting all and deleting)
                    input.select();
                    input.value = '';

                    // Set new value (like human typing)
                    input.value = text;

                    // Trigger events that browsers generate during typing
                    input.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));

                    // Trigger focus events (like human interaction)
                    input.dispatchEvent(new Event('focus', { bubbles: true }));
                }
            }, 50); // Small delay to mimic human reaction time

            return true;
        } catch (error) {
            console.error('Error setting input text:', error);
            return false;
        }
    }

    // Submit the prompt
    async submitPrompt(prompt) {
        // Rate limiting
        const now = Date.now();
        if (now - this.lastSubmissionTime < this.minSubmissionInterval) {
            console.log('Rate limiting: waiting before submission');
            await new Promise(resolve => setTimeout(resolve, this.minSubmissionInterval));
        }

        const input = this.findPromptInput();
        if (!input) {
            console.error('Could not find prompt input field');
            return false;
        }

        console.log(`Submitting prompt on ${this.isDiscord ? 'Discord' : 'Midjourney'}: ${prompt.substring(0, 50)}...`);

        // Set the prompt text
        if (!this.setInputText(input, prompt)) {
            console.error('Failed to set input text');
            return false;
        }

        // Wait a moment for the text to be processed
        await new Promise(resolve => setTimeout(resolve, 200));

        // Try to submit via Enter key first
        const enterSuccess = await this.submitViaEnter(input);
        if (enterSuccess) {
            this.lastSubmissionTime = Date.now();
            return true;
        }

        // Fallback to button click
        const buttonSuccess = await this.submitViaButton();
        if (buttonSuccess) {
            this.lastSubmissionTime = Date.now();
            return true;
        }

        console.error('Failed to submit prompt');
        return false;
    }

    // Submit via Enter key with human-like timing
    async submitViaEnter(input) {
        try {
            console.log('Submitting via Enter key (human-like)');

            // Wait a moment after text input (like human reading what they typed)
            await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));

            // Create realistic Enter key event sequence
            const keydownEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true,
                cancelable: true,
                isTrusted: false // Browser will set this, but we're being explicit
            });

            const keypressEvent = new KeyboardEvent('keypress', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true,
                cancelable: true
            });

            const keyupEvent = new KeyboardEvent('keyup', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true,
                cancelable: true
            });

            // Dispatch events in proper sequence (like real keyboard)
            input.dispatchEvent(keydownEvent);

            // Small delay between keydown and keypress (realistic timing)
            await new Promise(resolve => setTimeout(resolve, 10));
            input.dispatchEvent(keypressEvent);

            // Small delay between keypress and keyup
            await new Promise(resolve => setTimeout(resolve, 50));
            input.dispatchEvent(keyupEvent);

            // Wait for Midjourney to process the submission
            await new Promise(resolve => setTimeout(resolve, 800));

            // Check if submission was successful (input cleared)
            const currentValue = input.value || input.textContent || '';
            const wasCleared = currentValue.trim() === '';

            if (wasCleared) {
                console.log('Submission successful - input was cleared');
                return true;
            }

            // If input wasn't cleared, try form submission as backup
            console.log('Enter key didn\'t clear input, trying form submission');
            const form = input.closest('form');
            if (form) {
                form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
                await new Promise(resolve => setTimeout(resolve, 500));
                const newValue = input.value || input.textContent || '';
                return newValue.trim() === '';
            }

            return false;
        } catch (error) {
            console.error('Error submitting via Enter:', error);
            return false;
        }
    }

    // Submit via button click
    async submitViaButton() {
        try {
            const button = this.findSubmitButton();
            if (!button) {
                console.error('Could not find submit button');
                return false;
            }

            button.click();

            // Wait and verify submission
            await new Promise(resolve => setTimeout(resolve, 300));
            return true;
        } catch (error) {
            console.error('Error submitting via button:', error);
            return false;
        }
    }
} // End of PromptSubmitter class

    // Initialize the prompt submitter with error handling
    let promptSubmitter;
    try {
        promptSubmitter = new PromptSubmitter();
        console.log('PromptSubmitter initialized successfully');
    } catch (error) {
        console.error('Error initializing PromptSubmitter:', error);
    }

    // Enhanced message listener with better error handling
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        console.log('Content script received message:', request.action);

        if (request.action === 'postPrompt') {
            // Handle async operation properly
            (async () => {
                try {
                    console.log('Processing prompt submission request:', request.prompt.substring(0, 50) + '...');

                    if (!promptSubmitter) {
                        console.error('PromptSubmitter not initialized');
                        sendResponse({ success: false, error: 'PromptSubmitter not initialized' });
                        return;
                    }

                    const success = await promptSubmitter.submitPrompt(request.prompt);
                    console.log('Prompt submission result:', success);
                    sendResponse({
                        success,
                        message: success ? 'Prompt submitted successfully' : 'Submission failed',
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.error('Error in postPrompt:', error);
                    sendResponse({ success: false, error: error.message, timestamp: Date.now() });
                }
            })();

            return true; // Keep the message channel open for async response
        } else if (request.action === 'ping') {
            // Respond to ping requests with detailed info
            console.log('Responding to ping request');
            sendResponse({
                success: true,
                message: 'Content script is active and ready',
                hostname: window.location.hostname,
                url: window.location.href,
                timestamp: Date.now(),
                promptSubmitterReady: !!promptSubmitter
            });
            return true;
        } else if (request.action === 'checkStatus') {
            // Status check request
            sendResponse({
                success: true,
                loaded: true,
                hostname: window.location.hostname,
                promptSubmitterReady: !!promptSubmitter,
                timestamp: Date.now()
            });
            return true;
        }

        // Unknown action
        console.warn('Unknown action received:', request.action);
        sendResponse({ success: false, error: 'Unknown action: ' + request.action });
        return true;
    });

    // Enhanced initialization
    function initializeContentScript() {
        console.log(`Midjourney Prompt Manager v2.0 content script loaded on ${window.location.hostname}`);
        console.log('Current URL:', window.location.href);
        console.log('PromptSubmitter ready:', !!promptSubmitter);

        // Notify that script is ready
        try {
            chrome.runtime.sendMessage({
                action: 'contentScriptReady',
                hostname: window.location.hostname,
                url: window.location.href,
                timestamp: Date.now()
            });
        } catch (error) {
            console.log('Could not notify background script:', error.message);
        }

        // Set up periodic health check
        setInterval(() => {
            if (chrome && chrome.runtime) {
                try {
                    chrome.runtime.sendMessage({
                        action: 'healthCheck',
                        hostname: window.location.hostname,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    // Ignore errors for health checks
                }
            }
        }, 30000); // Every 30 seconds
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeContentScript);
    } else {
        initializeContentScript();
    }

} // End of duplicate prevention check
