// Enhanced content script for Midjourney Prompt Manager v2.0
// Supports both Midjourney.com and Discord.com

class PromptSubmitter {
    constructor() {
        this.isDiscord = window.location.hostname.includes('discord.com');
        this.isMidjourney = window.location.hostname.includes('midjourney.com');
        this.lastSubmissionTime = 0;
        this.minSubmissionInterval = 1000; // Minimum 1 second between submissions
    }

    // Find the appropriate input field based on the platform
    findPromptInput() {
        if (this.isMidjourney) {
            // Midjourney-specific selectors
            return document.getElementById('desktop_input_bar') ||
                   document.querySelector('input[placeholder*="imagine"]') ||
                   document.querySelector('input[placeholder*="prompt"]') ||
                   document.querySelector('textarea[placeholder*="imagine"]') ||
                   document.querySelector('textarea[placeholder*="prompt"]');
        } else if (this.isDiscord) {
            // Discord-specific selectors
            return document.querySelector('div[role="textbox"][data-slate-editor="true"]') ||
                   document.querySelector('div[contenteditable="true"][data-slate-editor="true"]') ||
                   document.querySelector('div[aria-label*="Message"]') ||
                   document.querySelector('div[class*="textArea"]') ||
                   document.querySelector('div[class*="messageInput"]');
        }
        return null;
    }

    // Find the submit button based on the platform
    findSubmitButton() {
        if (this.isMidjourney) {
            return document.querySelector('button[type="submit"]') ||
                   document.querySelector('button[aria-label*="generate"]') ||
                   document.querySelector('button[aria-label*="submit"]') ||
                   Array.from(document.querySelectorAll('button')).find(btn =>
                       btn.textContent.toLowerCase().includes('imagine') ||
                       btn.textContent.toLowerCase().includes('generate') ||
                       btn.textContent.toLowerCase().includes('submit')
                   );
        } else if (this.isDiscord) {
            return document.querySelector('button[aria-label*="Send message"]') ||
                   document.querySelector('button[type="submit"]') ||
                   document.querySelector('div[role="button"][aria-label*="Send"]') ||
                   Array.from(document.querySelectorAll('button')).find(btn =>
                       btn.getAttribute('aria-label')?.toLowerCase().includes('send')
                   );
        }
        return null;
    }

    // Set text in the input field
    setInputText(input, text) {
        if (!input) return false;

        try {
            // For Discord's contenteditable div
            if (input.contentEditable === 'true') {
                input.focus();

                // Clear existing content
                input.innerHTML = '';

                // Set new content
                input.textContent = text;

                // Trigger input events
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));

                // For Slate.js (Discord's editor)
                const slateEvent = new Event('beforeinput', { bubbles: true });
                slateEvent.inputType = 'insertText';
                slateEvent.data = text;
                input.dispatchEvent(slateEvent);

                return true;
            }
            // For regular input/textarea elements
            else if (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA') {
                input.focus();
                input.value = text;

                // Trigger events
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));

                return true;
            }
        } catch (error) {
            console.error('Error setting input text:', error);
        }

        return false;
    }

    // Submit the prompt
    async submitPrompt(prompt) {
        // Rate limiting
        const now = Date.now();
        if (now - this.lastSubmissionTime < this.minSubmissionInterval) {
            console.log('Rate limiting: waiting before submission');
            await new Promise(resolve => setTimeout(resolve, this.minSubmissionInterval));
        }

        const input = this.findPromptInput();
        if (!input) {
            console.error('Could not find prompt input field');
            return false;
        }

        console.log(`Submitting prompt on ${this.isDiscord ? 'Discord' : 'Midjourney'}: ${prompt.substring(0, 50)}...`);

        // Set the prompt text
        if (!this.setInputText(input, prompt)) {
            console.error('Failed to set input text');
            return false;
        }

        // Wait a moment for the text to be processed
        await new Promise(resolve => setTimeout(resolve, 200));

        // Try to submit via Enter key first
        const enterSuccess = await this.submitViaEnter(input);
        if (enterSuccess) {
            this.lastSubmissionTime = Date.now();
            return true;
        }

        // Fallback to button click
        const buttonSuccess = await this.submitViaButton();
        if (buttonSuccess) {
            this.lastSubmissionTime = Date.now();
            return true;
        }

        console.error('Failed to submit prompt');
        return false;
    }

    // Submit via Enter key
    async submitViaEnter(input) {
        try {
            const enterEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true,
                cancelable: true
            });

            input.dispatchEvent(enterEvent);

            // Wait and check if submission was successful
            await new Promise(resolve => setTimeout(resolve, 300));

            // Check if the input was cleared (indication of successful submission)
            const currentValue = input.value || input.textContent || '';
            return currentValue.trim() === '';
        } catch (error) {
            console.error('Error submitting via Enter:', error);
            return false;
        }
    }

    // Submit via button click
    async submitViaButton() {
        try {
            const button = this.findSubmitButton();
            if (!button) {
                console.error('Could not find submit button');
                return false;
            }

            button.click();

            // Wait and verify submission
            await new Promise(resolve => setTimeout(resolve, 300));
            return true;
        } catch (error) {
            console.error('Error submitting via button:', error);
            return false;
        }
    }
}

// Initialize the prompt submitter
const promptSubmitter = new PromptSubmitter();

// Message listener
chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
    if (request.action === 'postPrompt') {
        try {
            const success = await promptSubmitter.submitPrompt(request.prompt);
            sendResponse({ success });
        } catch (error) {
            console.error('Error in postPrompt:', error);
            sendResponse({ success: false, error: error.message });
        }
        return true; // Keep the message channel open for async response
    }
});

// Log when the content script loads
console.log(`Midjourney Prompt Manager v2.0 loaded on ${window.location.hostname}`);
