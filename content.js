chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'postPrompt') {
        // Find the prompt input field by its specific ID
        const promptInput = document.getElementById('desktop_input_bar');
        
        if (promptInput) {
            // Focus and set the prompt text
            promptInput.focus();
            promptInput.value = request.prompt;
            
            // Trigger input event to ensure the site recognizes the change
            promptInput.dispatchEvent(new Event('input', { bubbles: true }));
            promptInput.dispatchEvent(new Event('change', { bubbles: true }));
            
            // Wait a bit before sending Enter key events
            setTimeout(() => {
                // Try multiple types of Enter key events
                const events = [
                    new KeyboardEvent('keydown', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    }),
                    new KeyboardEvent('keypress', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    }),
                    new KeyboardEvent('keyup', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    })
                ];

                // Dispatch all enter key events
                events.forEach(event => promptInput.dispatchEvent(event));

                // Also try to submit the form if it exists
                const form = promptInput.closest('form');
                if (form) {
                    form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
                }

                // If nothing worked after a delay, try clicking the button
                setTimeout(() => {
                    if (promptInput.value === request.prompt) {
                        const submitButton = promptInput.closest('form')?.querySelector('button[type="submit"]') ||
                                          document.querySelector('button[aria-label*="generate" i]') ||
                                          Array.from(document.querySelectorAll('button')).find(btn => 
                                              btn.textContent.toLowerCase().includes('imagine') || 
                                              btn.textContent.toLowerCase().includes('generate')
                                          );
                        
                        if (submitButton) {
                            submitButton.click();
                        }
                    }
                }, 300);
            }, 200);
        }
    }
});
