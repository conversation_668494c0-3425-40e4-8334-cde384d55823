<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator for Midjourney Prompt Manager</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .icon-container { display: inline-block; margin: 10px; text-align: center; }
    </style>
</head>
<body>
    <h1>Midjourney Prompt Manager - Icon Generator</h1>
    <p>This page generates the required icon files for the Chrome extension.</p>
    
    <div id="icons"></div>
    
    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add rounded corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            const radius = size * 0.15;
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Add icon symbol (stylized "M" for Midjourney)
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🎨', size/2, size/2);
            
            return canvas;
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // Create icons
        const sizes = [16, 48, 128];
        const iconsContainer = document.getElementById('icons');
        
        sizes.forEach(size => {
            const container = document.createElement('div');
            container.className = 'icon-container';
            
            const canvas = createIcon(size);
            const button = document.createElement('button');
            button.textContent = `Download ${size}x${size}`;
            button.onclick = () => downloadCanvas(canvas, `icon${size}.png`);
            
            const label = document.createElement('div');
            label.textContent = `${size}x${size}`;
            
            container.appendChild(canvas);
            container.appendChild(document.createElement('br'));
            container.appendChild(label);
            container.appendChild(document.createElement('br'));
            container.appendChild(button);
            
            iconsContainer.appendChild(container);
        });
        
        // Auto-download all icons
        setTimeout(() => {
            sizes.forEach(size => {
                const canvas = createIcon(size);
                downloadCanvas(canvas, `icon${size}.png`);
            });
        }, 1000);
    </script>
</body>
</html>
