# Midjourney Prompt Manager v2.0

An advanced Chrome extension for batch management and automated submission of prompts to Midjourney and Discord.

## 🚀 New Features in v2.0

### Batch Submission System
- **Smart Batching**: Submits 7-10 prompts per batch (randomized)
- **Intelligent Timing**: 16-40 minute delays between batches
- **Micro-delays**: 1-10 second delays between individual prompts
- **Unpredictable Patterns**: Advanced randomization prevents detection

### Prompt List Management
- **File Storage**: Save/load prompt lists as JSON files
- **Organization**: Name and describe your prompt collections
- **Thumbnails**: Add visual thumbnails to identify lists quickly
- **Import/Export**: Share prompt lists with others
- **CRUD Operations**: Full create, read, update, delete functionality

### Advanced Configuration
- **Customizable Timing**: Adjust all delay parameters
- **Logging System**: Detailed operation logs for debugging
- **Notifications**: Optional system notifications for batch completion
- **Error Handling**: Comprehensive error tracking and recovery

### Enhanced UI
- **Modern Design**: Clean, professional interface
- **Responsive Layout**: Optimized for various screen sizes
- **Status Indicators**: Real-time batch processing status
- **Modal Dialogs**: Intuitive settings and list management

## 📋 Installation

1. Download or clone this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the extension folder
5. Pin the extension to your toolbar for easy access

## 🎯 Usage

### Basic Operation
1. Click the extension icon to open the popup
2. Enter your prompts (one per line) in the text area
3. Use "Post Next Prompt" for manual submission
4. Enable "Batch Processing" for automated submission

### Prompt List Management
1. Click "New" to create a prompt list
2. Enter prompts and click "Save List"
3. Use the dropdown to select and load existing lists
4. Export lists for backup or sharing
5. Import lists from JSON files

### Batch Processing
1. Select a prompt list from the dropdown
2. Toggle "Batch Processing" to start automated submission
3. Monitor progress in the status display
4. Use "Loop Mode" to repeat the list when finished

### Advanced Settings
1. Click "Configure" next to Advanced Settings
2. Adjust timing parameters as needed
3. Enable logging for detailed operation tracking
4. Configure notifications for batch completion

## ⚙️ Configuration Options

### Timing Parameters
- **Batch Size**: 7-10 prompts (configurable range)
- **Inter-batch Delay**: 16-40 minutes (configurable range)
- **Intra-batch Delay**: 1-10 seconds (configurable range)

### Features
- **Loop Mode**: Automatically restart when all prompts are submitted
- **Detailed Logging**: Track all operations for debugging
- **Notifications**: System notifications for important events
- **Error Recovery**: Automatic handling of submission failures

## 🔧 Technical Details

### Architecture
- **Manifest V3**: Latest Chrome extension standard
- **Service Worker**: Background processing for batch operations
- **Content Scripts**: Interact with Midjourney/Discord pages
- **Local Storage**: Secure local data storage

### Supported Platforms
- **Midjourney.com**: Official Midjourney website
- **Discord.com**: Discord servers with Midjourney bot

### Permissions Required
- `storage`: Save prompt lists and settings
- `activeTab`: Interact with current tab
- `scripting`: Inject content scripts
- `downloads`: Export prompt lists
- `notifications`: System notifications
- `unlimitedStorage`: Large prompt list storage

## 🛡️ Security & Privacy

- **Local Storage Only**: All data stored locally in Chrome
- **No External Servers**: No data transmitted to third parties
- **Minimal Permissions**: Only essential permissions requested
- **Open Source**: Full source code available for review

## 🐛 Troubleshooting

### Common Issues

**Prompts not submitting:**
- Ensure you're on Midjourney.com or Discord.com
- Check that the input field is visible and accessible
- Verify the extension has necessary permissions

**Batch processing stops unexpectedly:**
- Check the error log in advanced settings
- Ensure the target tab remains active
- Verify network connectivity

**Extension not loading:**
- Check Chrome developer console for errors
- Ensure all files are present in the extension folder
- Try reloading the extension

### Debug Information
- Enable detailed logging in advanced settings
- Check Chrome DevTools console (F12) for error messages
- Review error log in the extension popup

## 📊 Performance

### Resource Usage
- **Memory**: Minimal memory footprint (~5-10MB)
- **CPU**: Low CPU usage during operation
- **Storage**: Efficient data compression and cleanup
- **Network**: No external network requests

### Optimization Features
- **Asynchronous Operations**: Non-blocking UI operations
- **Error Recovery**: Automatic retry mechanisms
- **Storage Management**: Automatic cleanup of old logs
- **Efficient Rendering**: Optimized UI updates

## 🔄 Migration from v1.0

### Automatic Migration
- Existing prompts are automatically preserved
- Settings are migrated to new format
- No manual intervention required

### New Features Available
- Convert existing prompts to organized lists
- Enable batch processing for better efficiency
- Configure advanced timing parameters
- Export lists for backup

## 📝 File Formats

### Prompt List JSON Format
```json
{
  "id": "list_1234567890_abc123",
  "name": "My Prompt List",
  "description": "Collection of art prompts",
  "prompts": [
    "beautiful sunset over mountains --ar 16:9",
    "abstract digital art --v 6"
  ],
  "thumbnail": "data:image/jpeg;base64,...",
  "createdAt": 1234567890000,
  "version": "2.0"
}
```

## 🤝 Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support, please:
1. Check the troubleshooting section
2. Review the error logs
3. Open an issue on GitHub
4. Provide detailed error information

## 🔮 Future Enhancements

- Cloud synchronization of prompt lists
- Advanced prompt templating system
- Integration with additional AI platforms
- Collaborative prompt sharing features
- Advanced analytics and reporting
