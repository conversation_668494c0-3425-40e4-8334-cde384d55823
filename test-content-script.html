<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Content Script - Midjourney Prompt Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; text-align: center; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .input-field {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            margin: 10px 0;
        }
        .console-output {
            background: #f1f1f1;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Content Script Test Page</h1>
        
        <div class="test-section">
            <h3>📋 Instructions</h3>
            <ol>
                <li>Make sure the Midjourney Prompt Manager extension is loaded</li>
                <li>This page simulates Midjourney.com for testing</li>
                <li>Open the extension popup and click "Test Connection"</li>
                <li>Try posting a prompt using the extension</li>
                <li>Check the console output below for any errors</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🎯 Simulated Midjourney Input</h3>
            <p>This input field simulates the Midjourney prompt input:</p>
            <input type="text" id="desktop_input_bar" class="input-field" 
                   placeholder="Imagine a beautiful sunset over mountains...">
            <button onclick="clearInput()">Clear Input</button>
            <button onclick="simulateManualPost()">Simulate Manual Post</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="testResults">
                <div class="status info">Ready for testing...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 Console Output</h3>
            <div id="consoleOutput" class="console-output">Console messages will appear here...</div>
            <button onclick="clearConsole()">Clear Console</button>
        </div>

        <div class="test-section">
            <h3>🐛 Debug Information</h3>
            <div id="debugInfo">
                <p><strong>Page URL:</strong> <span id="pageUrl"></span></p>
                <p><strong>Extension Detected:</strong> <span id="extensionStatus">Checking...</span></p>
                <p><strong>Content Script Status:</strong> <span id="contentScriptStatus">Checking...</span></p>
            </div>
        </div>
    </div>

    <script>
        // Initialize debug info
        document.getElementById('pageUrl').textContent = window.location.href;

        // Check if extension is available
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            document.getElementById('extensionStatus').textContent = 'Available';
            document.getElementById('extensionStatus').style.color = 'green';
        } else {
            document.getElementById('extensionStatus').textContent = 'Not Available';
            document.getElementById('extensionStatus').style.color = 'red';
        }

        // Override console.log to capture messages
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsoleOutput(message, type = 'log') {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            output.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsoleOutput(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsoleOutput(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsoleOutput(args.join(' '), 'warn');
        };

        function addTestResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearInput() {
            document.getElementById('desktop_input_bar').value = '';
            addTestResult('Input field cleared', 'info');
        }

        function simulateManualPost() {
            const input = document.getElementById('desktop_input_bar');
            if (input.value.trim()) {
                addTestResult(`Manual post simulated: ${input.value}`, 'success');
                input.value = '';
            } else {
                addTestResult('No prompt to post', 'error');
            }
        }

        function clearConsole() {
            document.getElementById('consoleOutput').textContent = 'Console cleared...\n';
        }

        // Listen for input changes
        document.getElementById('desktop_input_bar').addEventListener('input', function(e) {
            if (e.target.value) {
                addTestResult(`Input detected: ${e.target.value}`, 'info');
            }
        });

        // Check for content script periodically
        let contentScriptChecks = 0;
        const checkContentScript = setInterval(() => {
            contentScriptChecks++;
            
            if (window.midjourneyPromptManagerLoaded) {
                document.getElementById('contentScriptStatus').textContent = 'Loaded and Active';
                document.getElementById('contentScriptStatus').style.color = 'green';
                addTestResult('Content script detected and loaded', 'success');
                clearInterval(checkContentScript);
            } else if (contentScriptChecks > 10) {
                document.getElementById('contentScriptStatus').textContent = 'Not Detected';
                document.getElementById('contentScriptStatus').style.color = 'red';
                addTestResult('Content script not detected after 10 seconds', 'error');
                clearInterval(checkContentScript);
            } else {
                document.getElementById('contentScriptStatus').textContent = `Checking... (${contentScriptChecks}/10)`;
                document.getElementById('contentScriptStatus').style.color = 'orange';
            }
        }, 1000);

        // Initial status
        addTestResult('Test page loaded successfully', 'success');
        console.log('Test page initialized');
    </script>
</body>
</html>
