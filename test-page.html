<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page - Midjourney Prompt Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .input-field {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            margin: 10px 0;
        }
        .input-field:focus {
            border-color: #007bff;
            outline: none;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Extension Test Page</h1>
        
        <div class="instructions">
            <h3>📋 Testing Instructions:</h3>
            <ol>
                <li>Make sure the Midjourney Prompt Manager extension is loaded</li>
                <li>Click the extension icon to open the popup</li>
                <li>Click "🔍 Test Connection" to verify content script</li>
                <li>Enter a test prompt in the extension popup</li>
                <li>Click "📤 Post Next Prompt" to test submission</li>
                <li>The prompt should appear in the input field below</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🎯 Midjourney-style Input Field</h3>
            <p>This simulates the Midjourney input field:</p>
            <input type="text" id="desktop_input_bar" class="input-field" placeholder="Imagine a beautiful sunset over mountains...">
            <button onclick="clearInput()">Clear</button>
            <button onclick="simulateSubmit()">Simulate Submit</button>
        </div>

        <div class="test-section">
            <h3>💬 Discord-style Input Field</h3>
            <p>This simulates the Discord message input:</p>
            <div id="discord-input" class="input-field" contenteditable="true" role="textbox" data-slate-editor="true" 
                 style="min-height: 50px; border: 2px solid #ddd;">
                Type your message here...
            </div>
            <button onclick="clearDiscordInput()">Clear</button>
            <button onclick="simulateDiscordSubmit()">Simulate Submit</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="testResults">
                <div class="status info">Ready for testing...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Manual Testing</h3>
            <p>You can also test manually:</p>
            <textarea id="manualPrompt" class="input-field" rows="3" placeholder="Enter a test prompt here...">a beautiful sunset over mountains --ar 16:9</textarea>
            <button onclick="insertPrompt()">Insert into Midjourney Field</button>
            <button onclick="insertDiscordPrompt()">Insert into Discord Field</button>
        </div>

        <div class="test-section">
            <h3>🐛 Debug Information</h3>
            <div id="debugInfo">
                <p><strong>Page URL:</strong> <span id="pageUrl"></span></p>
                <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
                <p><strong>Extension Status:</strong> <span id="extensionStatus">Checking...</span></p>
            </div>
        </div>
    </div>

    <script>
        // Initialize debug info
        document.getElementById('pageUrl').textContent = window.location.href;
        document.getElementById('userAgent').textContent = navigator.userAgent;

        // Check if extension is loaded
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            document.getElementById('extensionStatus').textContent = 'Chrome extension API available';
        } else {
            document.getElementById('extensionStatus').textContent = 'Chrome extension API not available';
        }

        function addTestResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearInput() {
            document.getElementById('desktop_input_bar').value = '';
            addTestResult('Midjourney input cleared', 'info');
        }

        function clearDiscordInput() {
            document.getElementById('discord-input').textContent = '';
            addTestResult('Discord input cleared', 'info');
        }

        function simulateSubmit() {
            const input = document.getElementById('desktop_input_bar');
            if (input.value.trim()) {
                addTestResult(`Midjourney prompt submitted: ${input.value}`, 'success');
                input.value = '';
            } else {
                addTestResult('No prompt to submit', 'error');
            }
        }

        function simulateDiscordSubmit() {
            const input = document.getElementById('discord-input');
            if (input.textContent.trim()) {
                addTestResult(`Discord message submitted: ${input.textContent}`, 'success');
                input.textContent = '';
            } else {
                addTestResult('No message to submit', 'error');
            }
        }

        function insertPrompt() {
            const prompt = document.getElementById('manualPrompt').value;
            const input = document.getElementById('desktop_input_bar');
            input.value = prompt;
            input.focus();
            addTestResult(`Inserted prompt into Midjourney field: ${prompt}`, 'success');
        }

        function insertDiscordPrompt() {
            const prompt = document.getElementById('manualPrompt').value;
            const input = document.getElementById('discord-input');
            input.textContent = prompt;
            input.focus();
            addTestResult(`Inserted prompt into Discord field: ${prompt}`, 'success');
        }

        // Listen for input changes
        document.getElementById('desktop_input_bar').addEventListener('input', function(e) {
            if (e.target.value) {
                addTestResult(`Midjourney input changed: ${e.target.value}`, 'info');
            }
        });

        document.getElementById('discord-input').addEventListener('input', function(e) {
            if (e.target.textContent) {
                addTestResult(`Discord input changed: ${e.target.textContent}`, 'info');
            }
        });

        // Simulate page load
        addTestResult('Test page loaded successfully', 'success');
    </script>
</body>
</html>
