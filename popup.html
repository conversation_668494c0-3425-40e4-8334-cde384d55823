<!DOCTYPE html>
<html>
<head>
  <title>Midjourney Prompt Manager</title>
  <style>
    body {
      width: 300px;
      padding: 10px;
      font-family: Arial, sans-serif;
    }
    textarea {
      width: 100%;
      height: 200px;
      margin-bottom: 10px;
    }
    button {
      width: 100%;
      padding: 8px;
      margin: 5px 0;
      background-color: #5865F2;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #4752C4;
    }
    #resetPrompt {
      background-color: #4CAF50;
    }
    #resetPrompt:hover {
      background-color: #45a049;
    }
    .replace-section {
      margin: 10px 0;
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
    .replace-section input {
      width: calc(50% - 5px);
      padding: 5px;
      margin: 2px;
      border: 1px solid #ccc;
      border-radius: 3px;
    }
    #replaceButton {
      background-color: #FF9800;
    }
    #replaceButton:hover {
      background-color: #F57C00;
    }
    .auto-section {
      margin: 10px 0;
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #2196F3;
    }
    input:checked + .slider:before {
      transform: translateX(26px);
    }
    #nextPostTime {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
  </style>
</head>
<body>
  <textarea id="promptList" placeholder="Enter your prompts (one per line)"></textarea>
  <div class="replace-section">
    <input type="text" id="findText" placeholder="Text to find">
    <input type="text" id="replaceText" placeholder="Replace with">
    <button id="replaceButton">Replace All</button>
  </div>
  <div class="auto-section">
    <span>Auto Post</span>
    <label class="switch">
      <input type="checkbox" id="autoSwitch">
      <span class="slider"></span>
    </label>
  </div>
  <div class="auto-section">
    <span>Loop</span>
    <label class="switch">
      <input type="checkbox" id="loopSwitch">
      <span class="slider"></span>
    </label>
  </div>
  <div class="auto-section">
    <span>12 Minutes Max</span>
    <label class="switch">
      <input type="checkbox" id="longIntervalSwitch">
      <span class="slider"></span>
    </label>
  </div>
  <div id="nextPostTime"></div>
  <button id="savePrompts">Save Prompts</button>
  <button id="postPrompt">Post Next Prompt</button>
  <button id="resetPrompt">Reset to First Prompt</button>
  <script src="popup.js"></script>
</body>
</html>
