<!DOCTYPE html>
<html>
<head>
  <title>Midjourney Prompt Manager v2.0</title>
  <style>
    body {
      width: 350px;
      padding: 15px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
    }
    .container {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    h1 {
      margin: 0 0 20px 0;
      font-size: 18px;
      text-align: center;
      color: #4a5568;
    }
    .prompt-list-section {
      margin-bottom: 20px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }
    .prompt-list-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .prompt-list-selector {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;
    }
    .prompt-list-dropdown {
      flex: 1;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: white;
    }
    .thumbnail-preview {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      object-fit: cover;
      border: 1px solid #ddd;
    }
    .list-actions {
      display: flex;
      gap: 5px;
      margin-top: 10px;
    }
    .list-actions button {
      flex: 1;
      padding: 6px;
      font-size: 12px;
      margin: 0;
    }
    textarea {
      width: 100%;
      height: 150px;
      margin-bottom: 10px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      resize: vertical;
    }
    button {
      width: 100%;
      padding: 10px;
      margin: 5px 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    button:active {
      transform: translateY(0);
    }
    .btn-success {
      background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    }
    .btn-warning {
      background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    }
    .btn-danger {
      background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    }
    .replace-section {
      margin: 15px 0;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }
    .replace-section input {
      width: calc(50% - 5px);
      padding: 8px;
      margin: 2px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .batch-section {
      margin: 15px 0;
      padding: 15px;
      background: #e8f5e8;
      border-radius: 8px;
      border: 1px solid #c8e6c9;
    }
    .batch-status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .batch-info {
      font-size: 12px;
      color: #666;
      margin: 5px 0;
    }
    .auto-section {
      margin: 10px 0;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #2196F3;
    }
    input:checked + .slider:before {
      transform: translateX(26px);
    }
    .status-display {
      font-size: 12px;
      color: #666;
      margin: 10px 0;
      padding: 8px;
      background: #f0f0f0;
      border-radius: 4px;
      text-align: center;
    }
    .file-input {
      display: none;
    }
    .file-label {
      display: inline-block;
      padding: 8px 12px;
      background: #6c757d;
      color: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      margin: 2px;
    }
    .file-label:hover {
      background: #5a6268;
    }
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
    }
    .modal-content {
      background-color: white;
      margin: 15% auto;
      padding: 20px;
      border-radius: 8px;
      width: 80%;
      max-width: 400px;
    }
    .close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }
    .close:hover {
      color: black;
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #4CAF50;
    }
    input:checked + .slider:before {
      transform: translateX(26px);
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Midjourney Batch Manager</h1>

    <!-- Prompt List Management Section -->
    <div class="prompt-list-section">
      <div class="prompt-list-header">
        <strong>Prompt Lists</strong>
        <button id="newListBtn" style="width: auto; padding: 4px 8px; font-size: 12px;">+ New</button>
      </div>

      <div class="prompt-list-selector">
        <select id="promptListDropdown" class="prompt-list-dropdown">
          <option value="">Select a prompt list...</option>
        </select>
        <img id="listThumbnail" class="thumbnail-preview" style="display: none;" alt="List thumbnail">
      </div>

      <div class="list-actions">
        <button id="loadListBtn" class="btn-success">Load</button>
        <button id="saveListBtn" class="btn-warning">Save</button>
        <button id="exportListBtn">Export</button>
        <button id="deleteListBtn" class="btn-danger">Delete</button>
      </div>

      <div style="margin-top: 10px;">
        <label for="importFile" class="file-label">Import List</label>
        <input type="file" id="importFile" class="file-input" accept=".json">
        <label for="thumbnailFile" class="file-label">Add Thumbnail</label>
        <input type="file" id="thumbnailFile" class="file-input" accept="image/*">
      </div>
    </div>

    <!-- Prompt Editor -->
    <textarea id="promptList" placeholder="Enter your prompts (one per line)&#10;&#10;Example:&#10;a beautiful sunset over mountains --ar 16:9&#10;abstract digital art with vibrant colors --v 6&#10;portrait of a wise old wizard --style raw"></textarea>
    <!-- Text Replace Section -->
    <div class="replace-section">
      <strong>Find & Replace</strong>
      <div style="margin-top: 8px;">
        <input type="text" id="findText" placeholder="Text to find">
        <input type="text" id="replaceText" placeholder="Replace with">
        <button id="replaceButton" class="btn-warning">Replace All</button>
      </div>
    </div>

    <!-- Batch Processing Section -->
    <div class="batch-section">
      <div class="batch-status">
        <strong>Batch Processing</strong>
        <label class="switch">
          <input type="checkbox" id="batchSwitch">
          <span class="slider"></span>
        </label>
      </div>
      <div class="batch-info" id="batchInfo">
        Batch Mode: 7-10 prompts per batch, 16-40 min between batches
      </div>
      <div class="batch-info" id="batchStatus"></div>
    </div>

    <!-- Settings Section -->
    <div class="auto-section">
      <span>Loop Mode</span>
      <label class="switch">
        <input type="checkbox" id="loopSwitch">
        <span class="slider"></span>
      </label>
    </div>

    <div class="auto-section">
      <span>Advanced Settings</span>
      <button id="settingsBtn" style="width: auto; padding: 4px 8px; font-size: 12px;">Configure</button>
    </div>

    <!-- Status Display -->
    <div class="status-display" id="statusDisplay">Ready to process prompts</div>

    <!-- Action Buttons -->
    <button id="testConnection">Test Connection</button>
    <button id="postPrompt">Post Next Prompt</button>
    <button id="resetPrompt" class="btn-success">Reset to First Prompt</button>

    <!-- Modals -->
    <div id="saveListModal" class="modal">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h3>Save Prompt List</h3>
        <input type="text" id="listNameInput" placeholder="Enter list name" style="width: 100%; margin: 10px 0; padding: 8px;">
        <textarea id="listDescriptionInput" placeholder="Optional description" style="width: 100%; height: 60px; margin: 10px 0; padding: 8px;"></textarea>
        <button id="confirmSaveBtn">Save List</button>
      </div>
    </div>

    <div id="settingsModal" class="modal">
      <div class="modal-content">
        <span class="close" id="settingsClose">&times;</span>
        <h3>Advanced Settings</h3>

        <div style="margin: 15px 0;">
          <label><strong>Batch Size Range:</strong></label>
          <div style="display: flex; gap: 10px; align-items: center; margin-top: 5px;">
            <input type="number" id="minBatchSize" min="1" max="20" value="7" style="width: 60px; padding: 4px;">
            <span>to</span>
            <input type="number" id="maxBatchSize" min="1" max="20" value="10" style="width: 60px; padding: 4px;">
            <span>prompts</span>
          </div>
        </div>

        <div style="margin: 15px 0;">
          <label><strong>Inter-batch Delay (minutes):</strong></label>
          <div style="display: flex; gap: 10px; align-items: center; margin-top: 5px;">
            <input type="number" id="minBatchDelay" min="1" max="120" value="16" style="width: 60px; padding: 4px;">
            <span>to</span>
            <input type="number" id="maxBatchDelay" min="1" max="120" value="40" style="width: 60px; padding: 4px;">
            <span>minutes</span>
          </div>
        </div>

        <div style="margin: 15px 0;">
          <label><strong>Intra-batch Delay (seconds):</strong></label>
          <div style="display: flex; gap: 10px; align-items: center; margin-top: 5px;">
            <input type="number" id="minPromptDelay" min="1" max="60" value="1" style="width: 60px; padding: 4px;">
            <span>to</span>
            <input type="number" id="maxPromptDelay" min="1" max="60" value="10" style="width: 60px; padding: 4px;">
            <span>seconds</span>
          </div>
        </div>

        <div style="margin: 15px 0;">
          <label>
            <input type="checkbox" id="enableLogging"> Enable detailed logging
          </label>
        </div>

        <div style="margin: 15px 0;">
          <label>
            <input type="checkbox" id="enableNotifications"> Enable notifications
          </label>
        </div>

        <button id="saveSettingsBtn">Save Settings</button>
        <button id="resetSettingsBtn" class="btn-warning" style="margin-top: 10px;">Reset to Defaults</button>
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
