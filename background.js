// Background service worker for Midjourney Prompt Manager v2.0
// Handles batch operations, file management, and timing coordination
// Optimized for performance and memory efficiency

class BatchManager {
    constructor() {
        this.currentBatch = null;
        this.batchTimer = null;
        this.promptTimer = null;
        this.isRunning = false;
        this.currentBatchIndex = 0;
        this.currentPromptIndex = 0;
        this.settingsCache = null;
        this.settingsCacheTime = 0;
        this.CACHE_DURATION = 30000; // 30 seconds cache
        this.MAX_RETRY_ATTEMPTS = 3;
        this.retryCount = 0;
    }

    // Get settings with caching for performance
    async getSettings() {
        const now = Date.now();

        // Return cached settings if still valid
        if (this.settingsCache && (now - this.settingsCacheTime) < this.CACHE_DURATION) {
            return this.settingsCache;
        }

        try {
            const result = await chrome.storage.local.get(['advancedSettings']);
            this.settingsCache = {
                minBatchSize: 7,
                maxBatchSize: 10,
                minBatchDelay: 16,
                maxBatchDelay: 40,
                minPromptDelay: 1,
                maxPromptDelay: 10,
                enableLogging: false,
                enableNotifications: true,
                ...(result.advancedSettings || {})
            };
            this.settingsCacheTime = now;
            return this.settingsCache;
        } catch (error) {
            // Return defaults if storage fails
            return {
                minBatchSize: 7,
                maxBatchSize: 10,
                minBatchDelay: 16,
                maxBatchDelay: 40,
                minPromptDelay: 1,
                maxPromptDelay: 10,
                enableLogging: false,
                enableNotifications: true
            };
        }
    }

    // Clear settings cache when settings are updated
    clearSettingsCache() {
        this.settingsCache = null;
        this.settingsCacheTime = 0;
    }

    // Generate random batch size using settings
    async getRandomBatchSize() {
        const settings = await this.getSettings();
        const range = settings.maxBatchSize - settings.minBatchSize + 1;
        return Math.floor(Math.random() * range) + settings.minBatchSize;
    }

    // Generate random inter-batch delay using settings
    async getRandomBatchDelay() {
        const settings = await this.getSettings();
        const minMs = settings.minBatchDelay * 60 * 1000;
        const maxMs = settings.maxBatchDelay * 60 * 1000;
        return Math.floor(Math.random() * (maxMs - minMs + 1)) + minMs;
    }

    // Generate random intra-batch delay using settings
    async getRandomPromptDelay() {
        const settings = await this.getSettings();
        const minMs = settings.minPromptDelay * 1000;
        const maxMs = settings.maxPromptDelay * 1000;
        return Math.floor(Math.random() * (maxMs - minMs + 1)) + minMs;
    }

    // Log with settings check
    async log(message, level = 'info') {
        const settings = await this.getSettings();
        if (settings.enableLogging) {
            console.log(`[BatchManager ${level.toUpperCase()}] ${message}`);
        }
    }

    // Start batch processing
    async startBatchProcessing(promptListId) {
        if (this.isRunning) {
            console.log('Batch processing already running');
            return;
        }

        try {
            const promptList = await this.getPromptList(promptListId);
            if (!promptList || !promptList.prompts || promptList.prompts.length === 0) {
                throw new Error('No prompts found in the selected list');
            }

            this.isRunning = true;
            this.currentBatchIndex = 0;
            this.currentPromptIndex = 0;

            await chrome.storage.local.set({
                batchProcessing: {
                    isRunning: true,
                    promptListId: promptListId,
                    currentBatchIndex: 0,
                    currentPromptIndex: 0,
                    startTime: Date.now()
                }
            });

            this.processBatch(promptList.prompts);
        } catch (error) {
            console.error('Error starting batch processing:', error);
            this.stopBatchProcessing();
        }
    }

    // Process a single batch
    async processBatch(allPrompts) {
        if (!this.isRunning) return;

        const batchSize = await this.getRandomBatchSize();
        const startIndex = this.currentPromptIndex;
        const endIndex = Math.min(startIndex + batchSize, allPrompts.length);
        const batchPrompts = allPrompts.slice(startIndex, endIndex);

        await this.log(`Processing batch ${this.currentBatchIndex + 1}, prompts ${startIndex + 1}-${endIndex}`);

        // Process prompts in this batch
        for (let i = 0; i < batchPrompts.length; i++) {
            if (!this.isRunning) break;

            const prompt = batchPrompts[i];
            await this.log(`Submitting prompt ${this.currentPromptIndex + 1}: ${prompt.substring(0, 50)}...`);
            await this.submitPrompt(prompt);

            // Update progress
            this.currentPromptIndex++;
            await this.updateProgress();

            // Wait before next prompt (except for the last one in batch)
            if (i < batchPrompts.length - 1) {
                const delay = await this.getRandomPromptDelay();
                await this.log(`Waiting ${delay}ms before next prompt`);
                await this.sleep(delay);
            }
        }

        // Check if we've processed all prompts
        if (this.currentPromptIndex >= allPrompts.length) {
            // Check if loop is enabled
            const settings = await chrome.storage.local.get(['loopPost']);
            if (settings.loopPost) {
                this.currentPromptIndex = 0;
                this.currentBatchIndex = 0;
            } else {
                this.stopBatchProcessing();
                return;
            }
        }

        // Schedule next batch
        if (this.isRunning) {
            this.currentBatchIndex++;
            const batchDelay = await this.getRandomBatchDelay();
            await this.log(`Next batch in ${Math.round(batchDelay / 60000)} minutes`);

            this.batchTimer = setTimeout(() => {
                this.processBatch(allPrompts);
            }, batchDelay);

            // Update next batch time for UI
            await chrome.storage.local.set({
                nextBatchTime: Date.now() + batchDelay
            });

            // Send notification if enabled
            const settings = await this.getSettings();
            if (settings.enableNotifications) {
                this.sendNotification(`Batch ${this.currentBatchIndex} completed. Next batch in ${Math.round(batchDelay / 60000)} minutes.`);
            }
        }
    }

    // Send notification
    async sendNotification(message) {
        try {
            await chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icon.png', // You'll need to add an icon
                title: 'Midjourney Batch Manager',
                message: message
            });
        } catch (error) {
            await this.log(`Notification error: ${error.message}`, 'error');
        }
    }

    // Submit a single prompt with error handling and retry logic
    async submitPrompt(prompt) {
        const maxRetries = this.MAX_RETRY_ATTEMPTS;

        for (let attempt = 0; attempt < maxRetries; attempt++) {
            try {
                const success = await this.attemptSubmission(prompt);
                if (success) {
                    this.retryCount = 0; // Reset retry count on success
                    return true;
                }

                if (attempt < maxRetries - 1) {
                    await this.log(`Submission attempt ${attempt + 1} failed, retrying...`, 'warn');
                    await this.sleep(1000 * (attempt + 1)); // Exponential backoff
                }
            } catch (error) {
                await this.log(`Submission attempt ${attempt + 1} error: ${error.message}`, 'error');
                if (attempt === maxRetries - 1) {
                    this.retryCount++;
                    return false;
                }
            }
        }

        return false;
    }

    // Single submission attempt
    async attemptSubmission(prompt) {
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                resolve(false);
            }, 10000); // 10 second timeout

            chrome.tabs.query({active: true, currentWindow: true}, async (tabs) => {
                try {
                    clearTimeout(timeout);

                    const activeTab = tabs[0];
                    if (!activeTab) {
                        await this.log('No active tab found', 'error');
                        resolve(false);
                        return;
                    }

                    if (!activeTab.url.includes('midjourney.com') && !activeTab.url.includes('discord.com')) {
                        await this.log(`Invalid tab URL: ${activeTab.url}`, 'error');
                        resolve(false);
                        return;
                    }

                    chrome.tabs.sendMessage(activeTab.id, {
                        action: 'postPrompt',
                        prompt: prompt
                    }, async (response) => {
                        if (chrome.runtime.lastError) {
                            await this.log(`Message sending error: ${chrome.runtime.lastError.message}`, 'error');
                            resolve(false);
                        } else if (response && response.success) {
                            await this.log('Prompt submitted successfully');
                            resolve(true);
                        } else {
                            await this.log(`Prompt submission failed: ${response?.error || 'Unknown error'}`, 'error');
                            resolve(false);
                        }
                    });
                } catch (error) {
                    await this.log(`Error in tab query callback: ${error.message}`, 'error');
                    resolve(false);
                }
            });
        });
    }

    // Stop batch processing with error handling
    async stopBatchProcessing() {
        try {
            this.isRunning = false;

            if (this.batchTimer) {
                clearTimeout(this.batchTimer);
                this.batchTimer = null;
            }

            if (this.promptTimer) {
                clearTimeout(this.promptTimer);
                this.promptTimer = null;
            }

            await chrome.storage.local.set({
                batchProcessing: {
                    isRunning: false,
                    stoppedAt: Date.now()
                },
                nextBatchTime: null
            });

            await this.log('Batch processing stopped');

            const settings = await this.getSettings();
            if (settings.enableNotifications) {
                this.sendNotification('Batch processing has been stopped.');
            }
        } catch (error) {
            await this.log(`Error stopping batch processing: ${error.message}`, 'error');
        }
    }

    // Update processing progress
    async updateProgress() {
        await chrome.storage.local.set({
            batchProcessing: {
                isRunning: this.isRunning,
                currentBatchIndex: this.currentBatchIndex,
                currentPromptIndex: this.currentPromptIndex
            }
        });
    }

    // Get prompt list by ID
    async getPromptList(listId) {
        const result = await chrome.storage.local.get(['promptLists']);
        const promptLists = result.promptLists || {};
        return promptLists[listId];
    }

    // Utility function for delays
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// File management utilities
class FileManager {
    // Export prompt list to file
    static async exportPromptList(listId, listData) {
        try {
            const exportData = {
                id: listId,
                name: listData.name,
                description: listData.description || '',
                prompts: listData.prompts,
                thumbnail: listData.thumbnail || null,
                createdAt: listData.createdAt || Date.now(),
                exportedAt: Date.now(),
                version: '2.0'
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });

            const url = URL.createObjectURL(blob);
            const filename = `midjourney-prompts-${listData.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;

            await chrome.downloads.download({
                url: url,
                filename: filename,
                saveAs: true
            });

            URL.revokeObjectURL(url);
            return true;
        } catch (error) {
            console.error('Error exporting prompt list:', error);
            return false;
        }
    }

    // Import prompt list from file content
    static async importPromptList(fileContent) {
        try {
            const data = JSON.parse(fileContent);
            
            // Validate required fields
            if (!data.name || !data.prompts || !Array.isArray(data.prompts)) {
                throw new Error('Invalid file format: missing required fields');
            }

            // Generate new ID to avoid conflicts
            const newId = 'list_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            
            const listData = {
                id: newId,
                name: data.name,
                description: data.description || '',
                prompts: data.prompts.filter(p => p && p.trim()),
                thumbnail: data.thumbnail || null,
                createdAt: Date.now(),
                importedAt: Date.now()
            };

            // Save to storage
            const result = await chrome.storage.local.get(['promptLists']);
            const promptLists = result.promptLists || {};
            promptLists[newId] = listData;
            
            await chrome.storage.local.set({ promptLists });
            return newId;
        } catch (error) {
            console.error('Error importing prompt list:', error);
            throw error;
        }
    }
}

// Initialize batch manager
const batchManager = new BatchManager();

// Error logging system
class ErrorLogger {
    static async logError(context, error, additionalData = {}) {
        const errorEntry = {
            timestamp: Date.now(),
            context: context,
            error: error.toString(),
            stack: error.stack || 'No stack trace available',
            additionalData: additionalData
        };

        try {
            const result = await chrome.storage.local.get(['errorLog']);
            const errorLog = result.errorLog || [];

            // Keep only last 100 errors to prevent storage bloat
            errorLog.push(errorEntry);
            if (errorLog.length > 100) {
                errorLog.splice(0, errorLog.length - 100);
            }

            await chrome.storage.local.set({ errorLog });
            console.error(`[ErrorLogger] ${context}:`, error);
        } catch (logError) {
            console.error('Failed to log error:', logError);
        }
    }

    static async getErrorLog() {
        try {
            const result = await chrome.storage.local.get(['errorLog']);
            return result.errorLog || [];
        } catch (error) {
            console.error('Failed to retrieve error log:', error);
            return [];
        }
    }

    static async clearErrorLog() {
        try {
            await chrome.storage.local.set({ errorLog: [] });
        } catch (error) {
            console.error('Failed to clear error log:', error);
        }
    }
}

// Enhanced message handling with comprehensive error handling
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    const handleAsync = async () => {
        try {
            switch (request.action) {
                case 'startBatchProcessing':
                    try {
                        await batchManager.startBatchProcessing(request.promptListId);
                        sendResponse({ success: true });
                    } catch (error) {
                        await ErrorLogger.logError('startBatchProcessing', error, { promptListId: request.promptListId });
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'stopBatchProcessing':
                    try {
                        await batchManager.stopBatchProcessing();
                        sendResponse({ success: true });
                    } catch (error) {
                        await ErrorLogger.logError('stopBatchProcessing', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'exportPromptList':
                    try {
                        const success = await FileManager.exportPromptList(request.listId, request.listData);
                        sendResponse({ success });
                    } catch (error) {
                        await ErrorLogger.logError('exportPromptList', error, { listId: request.listId });
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'importPromptList':
                    try {
                        const listId = await FileManager.importPromptList(request.fileContent);
                        sendResponse({ success: true, listId });
                    } catch (error) {
                        await ErrorLogger.logError('importPromptList', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'logError':
                    try {
                        await ErrorLogger.logError(request.context, new Error(request.error), {
                            timestamp: request.timestamp,
                            source: 'popup'
                        });
                        sendResponse({ success: true });
                    } catch (error) {
                        console.error('Failed to log error from popup:', error);
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'getErrorLog':
                    try {
                        const errorLog = await ErrorLogger.getErrorLog();
                        sendResponse({ success: true, errorLog });
                    } catch (error) {
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                case 'clearErrorLog':
                    try {
                        await ErrorLogger.clearErrorLog();
                        sendResponse({ success: true });
                    } catch (error) {
                        sendResponse({ success: false, error: error.message });
                    }
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            await ErrorLogger.logError('messageHandler', error, { action: request.action });
            sendResponse({ success: false, error: 'Internal error occurred' });
        }
    };

    handleAsync();
    return true; // Keep the message channel open for async response
});

// Restore batch processing on startup if it was running
chrome.runtime.onStartup.addListener(async () => {
    const result = await chrome.storage.local.get(['batchProcessing']);
    if (result.batchProcessing && result.batchProcessing.isRunning) {
        // Resume batch processing
        console.log('Resuming batch processing from startup');
        // Implementation would need to restore state and continue
    }
});
