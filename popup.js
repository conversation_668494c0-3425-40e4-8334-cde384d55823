document.addEventListener('DOMContentLoaded', function() {
    let autoPostTimer = null;
    let nextPostTimeDisplay = document.getElementById('nextPostTime');

    // Function to get random interval
    function getRandomInterval() {
        const longIntervalSwitch = document.getElementById('longIntervalSwitch');
        if (longIntervalSwitch.checked) {
            // 12 minutes max
            return Math.random() * (720000 - 10000) + 10000;
        } else {
            // 3 minutes max
            return Math.random() * (180000 - 10000) + 10000;
        }
    }

    // Function to format time remaining
    function formatTimeRemaining(ms) {
        const seconds = Math.floor((ms / 1000) % 60);
        const minutes = Math.floor(ms / 1000 / 60);
        return `Next post in: ${minutes}m ${seconds}s`;
    }

    // Function to update time display
    function updateTimeDisplay(endTime) {
        const updateTimer = setInterval(() => {
            const now = new Date().getTime();
            const timeLeft = endTime - now;
            
            if (timeLeft <= 0) {
                clearInterval(updateTimer);
                nextPostTimeDisplay.textContent = '';
            } else {
                nextPostTimeDisplay.textContent = formatTimeRemaining(timeLeft);
            }
        }, 1000);
        return updateTimer;
    }

    // Function to stop automation
    function stopAutomation() {
        const autoSwitch = document.getElementById('autoSwitch');
        autoSwitch.checked = false;
        if (autoPostTimer) {
            clearTimeout(autoPostTimer);
            autoPostTimer = null;
        }
        nextPostTimeDisplay.textContent = 'All prompts posted!';
        chrome.storage.local.set({ autoPost: false });
    }

    // Function to schedule next post
    function scheduleNextPost() {
        const interval = getRandomInterval();
        const nextPostTime = new Date().getTime() + interval;
        
        // Update display
        const displayTimer = updateTimeDisplay(nextPostTime);
        
        autoPostTimer = setTimeout(() => {
            clearInterval(displayTimer);
            postPrompt(true);
        }, interval);
        
        return autoPostTimer;
    }

    // Function to post prompt
    function postPrompt(isAuto = false) {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs[0].url.includes('midjourney.com')) {
                chrome.storage.local.get(['prompts', 'currentIndex'], function(result) {
                    if (result.prompts && result.prompts.length > 0) {
                        const currentIndex = result.currentIndex || 0;
                        const prompt = result.prompts[currentIndex];
                        
                        chrome.tabs.sendMessage(tabs[0].id, {
                            action: 'postPrompt',
                            prompt: prompt
                        });

                        const nextIndex = currentIndex + 1;
                        // Check if this was the last prompt
                        if (nextIndex >= result.prompts.length) {
                            chrome.storage.local.set({ currentIndex: 0 });
                            if (isAuto) {
                                const loopSwitch = document.getElementById('loopSwitch');
                                if (loopSwitch.checked) {
                                    // If loop is enabled, continue posting
                                    autoPostTimer = scheduleNextPost();
                                } else {
                                    // If loop is disabled, stop
                                    stopAutomation();
                                }
                            }
                        } else {
                            chrome.storage.local.set({ currentIndex: nextIndex }, function() {
                                // Schedule next post only if automation is still on and we're in auto mode
                                if (isAuto && document.getElementById('autoSwitch').checked) {
                                    autoPostTimer = scheduleNextPost();
                                }
                            });
                        }
                    }
                });
            }
        });
    }

    // Load saved prompts when popup opens
    chrome.storage.local.get(['prompts', 'currentIndex', 'autoPost', 'loopPost', 'longInterval'], function(result) {
        if (result.prompts) {
            document.getElementById('promptList').value = result.prompts.join('\n');
        }
        // Restore auto switch state
        if (result.autoPost) {
            document.getElementById('autoSwitch').checked = true;
            autoPostTimer = scheduleNextPost();
        }
        // Restore loop switch state
        if (result.loopPost) {
            document.getElementById('loopSwitch').checked = true;
        }
        // Restore long interval switch state
        if (result.longInterval) {
            document.getElementById('longIntervalSwitch').checked = true;
        }
    });

    // Auto switch handler
    document.getElementById('autoSwitch').addEventListener('change', function(e) {
        const isAuto = e.target.checked;
        chrome.storage.local.set({ autoPost: isAuto });
        
        if (isAuto) {
            chrome.storage.local.get(['prompts', 'currentIndex'], function(result) {
                if (result.prompts && result.prompts.length > 0) {
                    const currentIndex = result.currentIndex || 0;
                    if (currentIndex >= result.prompts.length) {
                        chrome.storage.local.set({ currentIndex: 0 });
                    }
                    autoPostTimer = scheduleNextPost();
                } else {
                    e.target.checked = false;
                    alert('No prompts saved! Please add and save some prompts first.');
                }
            });
        } else {
            if (autoPostTimer) {
                clearTimeout(autoPostTimer);
                autoPostTimer = null;
                nextPostTimeDisplay.textContent = '';
            }
        }
    });

    // Long interval switch handler
    document.getElementById('longIntervalSwitch').addEventListener('change', function(e) {
        const isLongInterval = e.target.checked;
        chrome.storage.local.set({ longInterval: isLongInterval });
        // If auto post is enabled, update the timer with new interval
        if (document.getElementById('autoSwitch').checked && autoPostTimer) {
            clearTimeout(autoPostTimer);
            autoPostTimer = scheduleNextPost();
        }
    });

    // Loop switch handler
    document.getElementById('loopSwitch').addEventListener('change', function(e) {
        const isLoop = e.target.checked;
        chrome.storage.local.set({ loopPost: isLoop });
    });

    // Save prompts
    document.getElementById('savePrompts').addEventListener('click', function() {
        const promptText = document.getElementById('promptList').value;
        const prompts = promptText.split('\n').filter(prompt => prompt.trim() !== '');
        
        chrome.storage.local.set({
            prompts: prompts,
            currentIndex: 0
        }, function() {
            alert('Prompts saved!');
        });
    });

    // Manual post prompt
    document.getElementById('postPrompt').addEventListener('click', () => postPrompt(false));

    // Reset to first prompt
    document.getElementById('resetPrompt').addEventListener('click', function() {
        chrome.storage.local.get(['prompts'], function(result) {
            if (result.prompts && result.prompts.length > 0) {
                chrome.storage.local.set({ currentIndex: 0 }, function() {
                    alert('Reset to first prompt!');
                });
            } else {
                alert('No prompts saved! Please add and save some prompts first.');
            }
        });
    });

    // Replace text in all prompts
    document.getElementById('replaceButton').addEventListener('click', function() {
        const findText = document.getElementById('findText').value;
        const replaceText = document.getElementById('replaceText').value;
        
        if (!findText) {
            alert('Please enter text to find!');
            return;
        }

        const promptList = document.getElementById('promptList');
        const currentText = promptList.value;
        
        const regex = new RegExp(findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        const newText = currentText.replace(regex, replaceText);
        
        if (currentText === newText) {
            alert('No matches found for: ' + findText);
        } else {
            promptList.value = newText;
            const prompts = newText.split('\n').filter(prompt => prompt.trim() !== '');
            chrome.storage.local.set({
                prompts: prompts,
                currentIndex: 0
            }, function() {
                alert('Text replaced and saved!');
            });
        }
    });
});
