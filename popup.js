document.addEventListener('DOMContentLoaded', function() {
    let statusUpdateTimer = null;
    let currentSelectedListId = null;
    let currentThumbnail = null;
    let promptListsCache = null;
    let lastCacheUpdate = 0;
    const CACHE_DURATION = 10000; // 10 seconds cache for prompt lists

    // Performance monitoring
    const performanceMetrics = {
        loadTime: Date.now(),
        operationCount: 0,
        errorCount: 0
    };

    // DOM Elements - cached for performance
    const elements = {
        promptList: document.getElementById('promptList'),
        promptListDropdown: document.getElementById('promptListDropdown'),
        listThumbnail: document.getElementById('listThumbnail'),
        batchSwitch: document.getElementById('batchSwitch'),
        loopSwitch: document.getElementById('loopSwitch'),
        statusDisplay: document.getElementById('statusDisplay'),
        batchStatus: document.getElementById('batchStatus'),
        saveListModal: document.getElementById('saveListModal'),
        listNameInput: document.getElementById('listNameInput'),
        listDescriptionInput: document.getElementById('listDescriptionInput'),
        importFile: document.getElementById('importFile'),
        thumbnailFile: document.getElementById('thumbnailFile')
    };

    // Debounce function for performance optimization
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Throttle function for performance optimization
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Utility Functions
    function showStatus(message, type = 'info') {
        elements.statusDisplay.textContent = message;
        elements.statusDisplay.className = 'status-display';
        if (type === 'error') {
            elements.statusDisplay.style.background = '#ffebee';
            elements.statusDisplay.style.color = '#c62828';
        } else if (type === 'success') {
            elements.statusDisplay.style.background = '#e8f5e8';
            elements.statusDisplay.style.color = '#2e7d32';
        } else {
            elements.statusDisplay.style.background = '#f0f0f0';
            elements.statusDisplay.style.color = '#666';
        }
    }

    function formatTimeRemaining(ms) {
        const seconds = Math.floor((ms / 1000) % 60);
        const minutes = Math.floor(ms / 1000 / 60);
        const hours = Math.floor(ms / 1000 / 60 / 60);

        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds}s`;
        }
        return `${minutes}m ${seconds}s`;
    }

    // Optimized prompt list loading with caching
    async function loadPromptLists(forceRefresh = false) {
        try {
            performanceMetrics.operationCount++;
            const now = Date.now();

            // Use cache if available and not expired
            if (!forceRefresh && promptListsCache && (now - lastCacheUpdate) < CACHE_DURATION) {
                updateDropdownFromCache();
                return;
            }

            const result = await chrome.storage.local.get(['promptLists']);
            const promptLists = result.promptLists || {};

            // Update cache
            promptListsCache = promptLists;
            lastCacheUpdate = now;

            updateDropdownFromCache();
            showStatus(`Loaded ${Object.keys(promptLists).length} prompt lists`);
        } catch (error) {
            performanceMetrics.errorCount++;
            console.error('Error loading prompt lists:', error);
            showStatus('Error loading prompt lists', 'error');
        }
    }

    // Update dropdown from cached data
    function updateDropdownFromCache() {
        if (!promptListsCache) return;

        // Use document fragment for better performance
        const fragment = document.createDocumentFragment();
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Select a prompt list...';
        fragment.appendChild(defaultOption);

        Object.entries(promptListsCache).forEach(([id, listData]) => {
            const option = document.createElement('option');
            option.value = id;
            option.textContent = listData.name;
            option.dataset.thumbnail = listData.thumbnail || '';
            fragment.appendChild(option);
        });

        // Clear and update dropdown in one operation
        elements.promptListDropdown.innerHTML = '';
        elements.promptListDropdown.appendChild(fragment);
    }

    // Clear cache when lists are modified
    function clearPromptListsCache() {
        promptListsCache = null;
        lastCacheUpdate = 0;
    }

    async function savePromptList(name, description = '') {
        try {
            const prompts = elements.promptList.value.split('\n').filter(p => p.trim());
            if (prompts.length === 0) {
                showStatus('No prompts to save', 'error');
                return;
            }

            const listId = 'list_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const listData = {
                id: listId,
                name: name,
                description: description,
                prompts: prompts,
                thumbnail: currentThumbnail,
                createdAt: Date.now()
            };

            const result = await chrome.storage.local.get(['promptLists']);
            const promptLists = result.promptLists || {};
            promptLists[listId] = listData;

            await chrome.storage.local.set({ promptLists });
            await loadPromptLists();

            // Select the newly created list
            elements.promptListDropdown.value = listId;
            currentSelectedListId = listId;
            updateThumbnailDisplay();

            showStatus(`Saved "${name}" with ${prompts.length} prompts`, 'success');
        } catch (error) {
            console.error('Error saving prompt list:', error);
            showStatus('Error saving prompt list', 'error');
        }
    }

    async function loadSelectedList() {
        try {
            const selectedId = elements.promptListDropdown.value;
            if (!selectedId) {
                showStatus('Please select a prompt list', 'error');
                return;
            }

            const result = await chrome.storage.local.get(['promptLists']);
            const promptLists = result.promptLists || {};
            const listData = promptLists[selectedId];

            if (!listData) {
                showStatus('Prompt list not found', 'error');
                return;
            }

            elements.promptList.value = listData.prompts.join('\n');
            currentSelectedListId = selectedId;
            updateThumbnailDisplay();

            showStatus(`Loaded "${listData.name}" with ${listData.prompts.length} prompts`, 'success');
        } catch (error) {
            console.error('Error loading selected list:', error);
            showStatus('Error loading prompt list', 'error');
        }
    }

    async function deleteSelectedList() {
        try {
            const selectedId = elements.promptListDropdown.value;
            if (!selectedId) {
                showStatus('Please select a prompt list to delete', 'error');
                return;
            }

            if (!confirm('Are you sure you want to delete this prompt list?')) {
                return;
            }

            const result = await chrome.storage.local.get(['promptLists']);
            const promptLists = result.promptLists || {};
            delete promptLists[selectedId];

            await chrome.storage.local.set({ promptLists });
            await loadPromptLists();

            elements.promptList.value = '';
            currentSelectedListId = null;
            updateThumbnailDisplay();

            showStatus('Prompt list deleted', 'success');
        } catch (error) {
            console.error('Error deleting prompt list:', error);
            showStatus('Error deleting prompt list', 'error');
        }
    }

    function updateThumbnailDisplay() {
        const selectedOption = elements.promptListDropdown.selectedOptions[0];
        if (selectedOption && selectedOption.dataset.thumbnail) {
            elements.listThumbnail.src = selectedOption.dataset.thumbnail;
            elements.listThumbnail.style.display = 'block';
        } else {
            elements.listThumbnail.style.display = 'none';
        }
    }

    // Safe message sending with connection handling
    async function sendMessageSafely(message, timeout = 5000) {
        return new Promise((resolve) => {
            const timeoutId = setTimeout(() => {
                resolve({ success: false, error: 'Connection timeout' });
            }, timeout);

            try {
                chrome.runtime.sendMessage(message, (response) => {
                    clearTimeout(timeoutId);

                    if (chrome.runtime.lastError) {
                        console.warn('Runtime error:', chrome.runtime.lastError.message);
                        resolve({ success: false, error: chrome.runtime.lastError.message });
                    } else if (response) {
                        resolve(response);
                    } else {
                        resolve({ success: false, error: 'No response received' });
                    }
                });
            } catch (error) {
                clearTimeout(timeoutId);
                resolve({ success: false, error: error.message });
            }
        });
    }

    // Batch Processing Functions
    async function startBatchProcessing() {
        try {
            if (!currentSelectedListId) {
                showStatus('Please select a prompt list first', 'error');
                elements.batchSwitch.checked = false;
                return;
            }

            showStatus('Starting batch processing...', 'info');

            const response = await sendMessageSafely({
                action: 'startBatchProcessing',
                promptListId: currentSelectedListId
            });

            if (response.success) {
                showStatus('Batch processing started', 'success');
                startStatusUpdates();
            } else {
                showStatus(`Error: ${response.error}`, 'error');
                elements.batchSwitch.checked = false;

                // If background script isn't responding, fall back to local processing
                if (response.error.includes('Connection') || response.error.includes('Receiving end')) {
                    showStatus('Background script not available, using local mode', 'warn');
                    await startLocalBatchProcessing();
                }
            }
        } catch (error) {
            console.error('Error starting batch processing:', error);
            showStatus('Error starting batch processing', 'error');
            elements.batchSwitch.checked = false;
        }
    }

    // Local batch processing fallback
    async function startLocalBatchProcessing() {
        try {
            const result = await chrome.storage.local.get(['promptLists']);
            const promptLists = result.promptLists || {};
            const listData = promptLists[currentSelectedListId];

            if (!listData || !listData.prompts || listData.prompts.length === 0) {
                showStatus('No prompts found in selected list', 'error');
                elements.batchSwitch.checked = false;
                return;
            }

            showStatus('Running in local mode - manual batch processing', 'warn');

            // Store local batch state
            await chrome.storage.local.set({
                localBatchProcessing: {
                    isRunning: true,
                    promptListId: currentSelectedListId,
                    currentIndex: 0,
                    prompts: listData.prompts
                }
            });

            startStatusUpdates();
        } catch (error) {
            console.error('Error starting local batch processing:', error);
            showStatus('Error starting local batch processing', 'error');
            elements.batchSwitch.checked = false;
        }
    }

    async function stopBatchProcessing() {
        try {
            const response = await sendMessageSafely({
                action: 'stopBatchProcessing'
            });

            if (response.success || response.error.includes('Connection')) {
                // Also stop local processing
                await chrome.storage.local.set({
                    localBatchProcessing: { isRunning: false }
                });

                showStatus('Batch processing stopped', 'success');
                stopStatusUpdates();
            } else {
                showStatus(`Error: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('Error stopping batch processing:', error);
            showStatus('Error stopping batch processing', 'error');
        }
    }

    function startStatusUpdates() {
        if (statusUpdateTimer) {
            clearInterval(statusUpdateTimer);
        }

        statusUpdateTimer = setInterval(async () => {
            try {
                const result = await chrome.storage.local.get(['batchProcessing', 'localBatchProcessing', 'nextBatchTime']);
                const batchData = result.batchProcessing;
                const localBatchData = result.localBatchProcessing;

                // Check background batch processing first
                if (batchData && batchData.isRunning) {
                    let statusText = `Batch ${batchData.currentBatchIndex + 1} - Prompt ${batchData.currentPromptIndex + 1}`;

                    if (result.nextBatchTime) {
                        const timeLeft = result.nextBatchTime - Date.now();
                        if (timeLeft > 0) {
                            statusText += ` | Next batch in: ${formatTimeRemaining(timeLeft)}`;
                        }
                    }

                    elements.batchStatus.textContent = statusText;
                }
                // Check local batch processing
                else if (localBatchData && localBatchData.isRunning) {
                    const currentIndex = localBatchData.currentIndex || 0;
                    const totalPrompts = localBatchData.prompts ? localBatchData.prompts.length : 0;
                    elements.batchStatus.textContent = `Local Mode - Prompt ${currentIndex + 1}/${totalPrompts} (Manual submission required)`;
                }
                // No batch processing running
                else {
                    elements.batchStatus.textContent = 'Batch processing stopped';
                    elements.batchSwitch.checked = false;
                    stopStatusUpdates();
                }
            } catch (error) {
                console.error('Error updating status:', error);
            }
        }, 1000);
    }

    function stopStatusUpdates() {
        if (statusUpdateTimer) {
            clearInterval(statusUpdateTimer);
            statusUpdateTimer = null;
        }
        elements.batchStatus.textContent = '';
    }

    // File Management Functions
    async function exportPromptList() {
        try {
            const selectedId = elements.promptListDropdown.value;
            if (!selectedId) {
                showStatus('Please select a prompt list to export', 'error');
                return;
            }

            const result = await chrome.storage.local.get(['promptLists']);
            const promptLists = result.promptLists || {};
            const listData = promptLists[selectedId];

            if (!listData) {
                showStatus('Prompt list not found', 'error');
                return;
            }

            const response = await sendMessageSafely({
                action: 'exportPromptList',
                listId: selectedId,
                listData: listData
            });

            if (response.success) {
                showStatus('Prompt list exported successfully', 'success');
            } else if (response.error.includes('Connection')) {
                // Fallback to manual download
                await exportPromptListManually(selectedId, listData);
            } else {
                showStatus(`Export failed: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('Error exporting prompt list:', error);
            showStatus('Error exporting prompt list', 'error');
        }
    }

    // Manual export fallback
    async function exportPromptListManually(listId, listData) {
        try {
            const exportData = {
                id: listId,
                name: listData.name,
                description: listData.description || '',
                prompts: listData.prompts,
                thumbnail: listData.thumbnail || null,
                createdAt: listData.createdAt || Date.now(),
                exportedAt: Date.now(),
                version: '2.0'
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });

            const url = URL.createObjectURL(blob);
            const filename = `midjourney-prompts-${listData.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;

            // Create download link
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showStatus('Prompt list exported successfully (manual download)', 'success');
        } catch (error) {
            console.error('Error in manual export:', error);
            showStatus('Error exporting prompt list', 'error');
        }
    }

    async function importPromptList(fileContent) {
        try {
            const response = await sendMessageSafely({
                action: 'importPromptList',
                fileContent: fileContent
            });

            if (response.success) {
                await loadPromptLists(true); // Force refresh
                elements.promptListDropdown.value = response.listId;
                currentSelectedListId = response.listId;
                await loadSelectedList();
                showStatus('Prompt list imported successfully', 'success');
            } else if (response.error.includes('Connection')) {
                // Fallback to manual import
                const listId = await importPromptListManually(fileContent);
                if (listId) {
                    await loadPromptLists(true);
                    elements.promptListDropdown.value = listId;
                    currentSelectedListId = listId;
                    await loadSelectedList();
                    showStatus('Prompt list imported successfully (manual)', 'success');
                }
            } else {
                showStatus(`Import failed: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('Error importing prompt list:', error);
            showStatus('Error importing prompt list', 'error');
        }
    }

    // Manual import fallback
    async function importPromptListManually(fileContent) {
        try {
            const data = JSON.parse(fileContent);

            // Validate required fields
            if (!data.name || !data.prompts || !Array.isArray(data.prompts)) {
                showStatus('Invalid file format: missing required fields', 'error');
                return null;
            }

            // Generate new ID to avoid conflicts
            const newId = 'list_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            const listData = {
                id: newId,
                name: data.name,
                description: data.description || '',
                prompts: data.prompts.filter(p => p && p.trim()),
                thumbnail: data.thumbnail || null,
                createdAt: Date.now(),
                importedAt: Date.now()
            };

            // Save to storage
            const result = await chrome.storage.local.get(['promptLists']);
            const promptLists = result.promptLists || {};
            promptLists[newId] = listData;

            await chrome.storage.local.set({ promptLists });
            clearPromptListsCache(); // Clear cache to force refresh

            return newId;
        } catch (error) {
            console.error('Error in manual import:', error);
            showStatus('Error importing prompt list: ' + error.message, 'error');
            return null;
        }
    }

    // Enhanced prompt posting with content script injection
    async function postPrompt() {
        try {
            const prompts = elements.promptList.value.split('\n').filter(p => p.trim());
            if (prompts.length === 0) {
                showStatus('No prompts to post', 'error');
                return;
            }

            showStatus('Preparing to post prompt...', 'info');

            const tabs = await chrome.tabs.query({active: true, currentWindow: true});
            const activeTab = tabs[0];

            if (!activeTab) {
                showStatus('No active tab found', 'error');
                return;
            }

            if (!activeTab.url.includes('midjourney.com') && !activeTab.url.includes('discord.com')) {
                showStatus('Please navigate to Midjourney.com or Discord.com first', 'error');
                return;
            }

            // Get current index from storage or use 0
            const result = await chrome.storage.local.get(['currentIndex']);
            const currentIndex = result.currentIndex || 0;
            const prompt = prompts[currentIndex % prompts.length];

            showStatus(`Posting prompt ${currentIndex + 1}/${prompts.length}...`, 'info');

            // Try to inject content script first (in case it's not loaded)
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: activeTab.id },
                    files: ['content.js']
                });
            } catch (injectionError) {
                console.log('Content script already injected or injection failed:', injectionError.message);
            }

            // Wait a moment for script to initialize
            await new Promise(resolve => setTimeout(resolve, 500));

            // Send message to content script with timeout
            const response = await sendMessageToTab(activeTab.id, {
                action: 'postPrompt',
                prompt: prompt
            });

            if (response && response.success) {
                // Update index for next prompt
                const nextIndex = (currentIndex + 1) % prompts.length;
                await chrome.storage.local.set({ currentIndex: nextIndex });

                showStatus(`Posted prompt ${currentIndex + 1}/${prompts.length}`, 'success');
            } else {
                // Fallback to manual method
                await postPromptManually(activeTab.id, prompt);
            }

        } catch (error) {
            console.error('Error posting prompt:', error);
            showStatus('Error posting prompt: ' + error.message, 'error');
        }
    }

    // Send message to tab with timeout and error handling
    function sendMessageToTab(tabId, message, timeout = 5000) {
        return new Promise((resolve) => {
            const timeoutId = setTimeout(() => {
                resolve({ success: false, error: 'Timeout' });
            }, timeout);

            try {
                chrome.tabs.sendMessage(tabId, message, (response) => {
                    clearTimeout(timeoutId);

                    if (chrome.runtime.lastError) {
                        console.warn('Tab message error:', chrome.runtime.lastError.message);
                        resolve({ success: false, error: chrome.runtime.lastError.message });
                    } else {
                        resolve(response || { success: false, error: 'No response' });
                    }
                });
            } catch (error) {
                clearTimeout(timeoutId);
                resolve({ success: false, error: error.message });
            }
        });
    }

    // Manual prompt posting fallback
    async function postPromptManually(tabId, prompt) {
        try {
            showStatus('Using manual posting method...', 'info');

            // Inject a simple script directly
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: (promptText) => {
                    // Find input field
                    let input = document.getElementById('desktop_input_bar') ||
                               document.querySelector('input[placeholder*="imagine" i]') ||
                               document.querySelector('textarea[placeholder*="imagine" i]') ||
                               document.querySelector('div[role="textbox"]') ||
                               document.querySelector('div[contenteditable="true"]') ||
                               document.querySelector('input[type="text"]') ||
                               document.querySelector('textarea');

                    if (input) {
                        // Focus and set text
                        input.focus();

                        if (input.contentEditable === 'true') {
                            input.textContent = promptText;
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                        } else {
                            input.value = promptText;
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            input.dispatchEvent(new Event('change', { bubbles: true }));
                        }

                        return { success: true, message: 'Prompt inserted successfully' };
                    } else {
                        return { success: false, error: 'No input field found' };
                    }
                },
                args: [prompt]
            });

            showStatus('Prompt inserted - please press Enter to submit', 'success');

        } catch (error) {
            console.error('Manual posting failed:', error);
            showStatus('Manual posting failed: ' + error.message, 'error');
        }
    }

    function resetPromptIndex() {
        chrome.storage.local.set({ currentIndex: 0 }, function() {
            showStatus('Reset to first prompt', 'success');
        });
    }

    // Test content script connection
    async function testContentScript() {
        try {
            const tabs = await chrome.tabs.query({active: true, currentWindow: true});
            const activeTab = tabs[0];

            if (!activeTab) {
                showStatus('No active tab found', 'error');
                return;
            }

            if (!activeTab.url.includes('midjourney.com') && !activeTab.url.includes('discord.com')) {
                showStatus('Please navigate to Midjourney.com or Discord.com to test', 'warn');
                return;
            }

            showStatus('Testing content script connection...', 'info');

            // Try to inject content script
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: activeTab.id },
                    files: ['content.js']
                });
            } catch (injectionError) {
                console.log('Content script injection result:', injectionError.message);
            }

            // Test ping
            const response = await sendMessageToTab(activeTab.id, { action: 'ping' });

            if (response && response.success) {
                showStatus('Content script is working correctly', 'success');
            } else {
                showStatus('Content script not responding - will use manual mode', 'warn');
            }

        } catch (error) {
            console.error('Error testing content script:', error);
            showStatus('Error testing content script: ' + error.message, 'error');
        }
    }

    // Text replacement function
    function replaceTextInPrompts() {
        const findText = document.getElementById('findText').value;
        const replaceText = document.getElementById('replaceText').value;

        if (!findText) {
            showStatus('Please enter text to find', 'error');
            return;
        }

        const currentText = elements.promptList.value;
        const regex = new RegExp(findText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        const newText = currentText.replace(regex, replaceText);

        if (currentText === newText) {
            showStatus(`No matches found for: ${findText}`, 'error');
        } else {
            elements.promptList.value = newText;
            showStatus('Text replaced successfully', 'success');
        }
    }

    // Thumbnail handling
    function handleThumbnailUpload(file) {
        if (!file || !file.type.startsWith('image/')) {
            showStatus('Please select a valid image file', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            // Resize image to thumbnail size
            const img = new Image();
            img.onload = function() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Set thumbnail dimensions
                const maxSize = 100;
                let { width, height } = img;

                if (width > height) {
                    if (width > maxSize) {
                        height = (height * maxSize) / width;
                        width = maxSize;
                    }
                } else {
                    if (height > maxSize) {
                        width = (width * maxSize) / height;
                        height = maxSize;
                    }
                }

                canvas.width = width;
                canvas.height = height;
                ctx.drawImage(img, 0, 0, width, height);

                currentThumbnail = canvas.toDataURL('image/jpeg', 0.8);
                showStatus('Thumbnail added', 'success');
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    // Settings Management
    const defaultSettings = {
        minBatchSize: 7,
        maxBatchSize: 10,
        minBatchDelay: 16,
        maxBatchDelay: 40,
        minPromptDelay: 1,
        maxPromptDelay: 10,
        enableLogging: false,
        enableNotifications: true
    };

    async function loadSettings() {
        try {
            const result = await chrome.storage.local.get(['advancedSettings']);
            const settings = { ...defaultSettings, ...(result.advancedSettings || {}) };

            document.getElementById('minBatchSize').value = settings.minBatchSize;
            document.getElementById('maxBatchSize').value = settings.maxBatchSize;
            document.getElementById('minBatchDelay').value = settings.minBatchDelay;
            document.getElementById('maxBatchDelay').value = settings.maxBatchDelay;
            document.getElementById('minPromptDelay').value = settings.minPromptDelay;
            document.getElementById('maxPromptDelay').value = settings.maxPromptDelay;
            document.getElementById('enableLogging').checked = settings.enableLogging;
            document.getElementById('enableNotifications').checked = settings.enableNotifications;

            return settings;
        } catch (error) {
            console.error('Error loading settings:', error);
            return defaultSettings;
        }
    }

    async function saveSettings() {
        try {
            const settings = {
                minBatchSize: parseInt(document.getElementById('minBatchSize').value),
                maxBatchSize: parseInt(document.getElementById('maxBatchSize').value),
                minBatchDelay: parseInt(document.getElementById('minBatchDelay').value),
                maxBatchDelay: parseInt(document.getElementById('maxBatchDelay').value),
                minPromptDelay: parseInt(document.getElementById('minPromptDelay').value),
                maxPromptDelay: parseInt(document.getElementById('maxPromptDelay').value),
                enableLogging: document.getElementById('enableLogging').checked,
                enableNotifications: document.getElementById('enableNotifications').checked
            };

            // Validate settings
            if (settings.minBatchSize > settings.maxBatchSize) {
                showStatus('Min batch size cannot be greater than max batch size', 'error');
                return;
            }
            if (settings.minBatchDelay > settings.maxBatchDelay) {
                showStatus('Min batch delay cannot be greater than max batch delay', 'error');
                return;
            }
            if (settings.minPromptDelay > settings.maxPromptDelay) {
                showStatus('Min prompt delay cannot be greater than max prompt delay', 'error');
                return;
            }

            await chrome.storage.local.set({ advancedSettings: settings });
            showStatus('Settings saved successfully', 'success');
            document.getElementById('settingsModal').style.display = 'none';
        } catch (error) {
            console.error('Error saving settings:', error);
            showStatus('Error saving settings', 'error');
        }
    }

    async function resetSettings() {
        try {
            await chrome.storage.local.set({ advancedSettings: defaultSettings });
            await loadSettings();
            showStatus('Settings reset to defaults', 'success');
        } catch (error) {
            console.error('Error resetting settings:', error);
            showStatus('Error resetting settings', 'error');
        }
    }

    // Event Handlers

    // Prompt List Management
    elements.promptListDropdown.addEventListener('change', updateThumbnailDisplay);

    document.getElementById('newListBtn').addEventListener('click', () => {
        elements.saveListModal.style.display = 'block';
        elements.listNameInput.value = '';
        elements.listDescriptionInput.value = '';
    });

    document.getElementById('loadListBtn').addEventListener('click', loadSelectedList);

    document.getElementById('saveListBtn').addEventListener('click', () => {
        elements.saveListModal.style.display = 'block';
        elements.listNameInput.value = '';
        elements.listDescriptionInput.value = '';
    });

    document.getElementById('exportListBtn').addEventListener('click', exportPromptList);
    document.getElementById('deleteListBtn').addEventListener('click', deleteSelectedList);

    // Modal handling
    document.querySelector('.close').addEventListener('click', () => {
        elements.saveListModal.style.display = 'none';
    });

    document.getElementById('settingsClose').addEventListener('click', () => {
        document.getElementById('settingsModal').style.display = 'none';
    });

    document.getElementById('settingsBtn').addEventListener('click', async () => {
        await loadSettings();
        document.getElementById('settingsModal').style.display = 'block';
    });

    document.getElementById('saveSettingsBtn').addEventListener('click', saveSettings);
    document.getElementById('resetSettingsBtn').addEventListener('click', resetSettings);

    document.getElementById('confirmSaveBtn').addEventListener('click', async () => {
        const name = elements.listNameInput.value.trim();
        const description = elements.listDescriptionInput.value.trim();

        if (!name) {
            showStatus('Please enter a list name', 'error');
            return;
        }

        await savePromptList(name, description);
        elements.saveListModal.style.display = 'none';
        currentThumbnail = null; // Reset thumbnail after saving
    });

    // File uploads
    elements.importFile.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => importPromptList(e.target.result);
            reader.readAsText(file);
        }
    });

    elements.thumbnailFile.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            handleThumbnailUpload(file);
        }
    });

    // Batch processing
    elements.batchSwitch.addEventListener('change', (e) => {
        if (e.target.checked) {
            startBatchProcessing();
        } else {
            stopBatchProcessing();
        }
    });

    // Loop setting
    elements.loopSwitch.addEventListener('change', (e) => {
        chrome.storage.local.set({ loopPost: e.target.checked });
    });

    // Action buttons
    document.getElementById('testConnection').addEventListener('click', testContentScript);
    document.getElementById('postPrompt').addEventListener('click', postPrompt);
    document.getElementById('resetPrompt').addEventListener('click', resetPromptIndex);
    document.getElementById('replaceButton').addEventListener('click', replaceTextInPrompts);

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === elements.saveListModal) {
            elements.saveListModal.style.display = 'none';
        }
        if (e.target === document.getElementById('settingsModal')) {
            document.getElementById('settingsModal').style.display = 'none';
        }
    });

    // Error handling wrapper
    function handleError(error, context = 'Unknown operation') {
        console.error(`Error in ${context}:`, error);
        showStatus(`Error: ${error.message || 'Unknown error occurred'}`, 'error');

        // Log to background if logging is enabled
        chrome.storage.local.get(['advancedSettings']).then(result => {
            const settings = result.advancedSettings || {};
            if (settings.enableLogging) {
                chrome.runtime.sendMessage({
                    action: 'logError',
                    context: context,
                    error: error.message || error.toString(),
                    timestamp: Date.now()
                });
            }
        });
    }

    // Check background script connection
    async function checkBackgroundConnection() {
        try {
            const response = await sendMessageSafely({ action: 'ping' }, 2000);
            return response.success;
        } catch (error) {
            return false;
        }
    }

    // Enhanced initialization with better error handling
    async function initialize() {
        try {
            showStatus('Loading extension...', 'info');

            // Check background script connection
            const backgroundConnected = await checkBackgroundConnection();
            if (!backgroundConnected) {
                showStatus('Background script not available - using local mode', 'warn');
            }

            // Load prompt lists with timeout
            const loadPromiseTimeout = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Timeout loading prompt lists')), 5000)
            );

            await Promise.race([loadPromptLists(), loadPromiseTimeout]);

            // Restore settings with error handling
            try {
                const result = await chrome.storage.local.get(['loopPost', 'batchProcessing', 'localBatchProcessing', 'advancedSettings']);

                if (result.loopPost) {
                    elements.loopSwitch.checked = true;
                }

                // Check if batch processing is running
                if ((result.batchProcessing && result.batchProcessing.isRunning) ||
                    (result.localBatchProcessing && result.localBatchProcessing.isRunning)) {
                    elements.batchSwitch.checked = true;
                    startStatusUpdates();
                    showStatus('Batch processing is running', 'success');
                } else {
                    const statusMsg = backgroundConnected ?
                        'Extension loaded successfully' :
                        'Extension loaded (local mode only)';
                    showStatus(statusMsg, backgroundConnected ? 'success' : 'warn');
                }

                // Validate settings
                if (result.advancedSettings) {
                    const settings = result.advancedSettings;
                    if (settings.minBatchSize > settings.maxBatchSize ||
                        settings.minBatchDelay > settings.maxBatchDelay ||
                        settings.minPromptDelay > settings.maxPromptDelay) {
                        showStatus('Warning: Invalid settings detected. Please check configuration.', 'error');
                    }
                }
            } catch (settingsError) {
                handleError(settingsError, 'Settings restoration');
            }

        } catch (error) {
            handleError(error, 'Extension initialization');
        }
    }

    // Global error handler for unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
        handleError(event.reason, 'Unhandled promise rejection');
        event.preventDefault();
    });

    // Global error handler for uncaught exceptions
    window.addEventListener('error', (event) => {
        handleError(event.error, 'Uncaught exception');
    });

    // Start initialization
    initialize();
});
