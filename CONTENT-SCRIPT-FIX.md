# Content Script Connection Fix Guide

## 🔧 **Issue: "Content script not responding - will use manual mode"**

This error means the extension can't communicate with the Midjourney/Discord webpage, which prevents automatic posting.

---

## ✅ **What's Been Fixed**

### **Enhanced Content Script Injection:**
- ✅ **Better URL matching** in manifest (includes all Discord subdomains)
- ✅ **Forced injection** when content script doesn't respond
- ✅ **Multiple retry attempts** with longer timeouts
- ✅ **Comprehensive error handling** and diagnostics

### **Improved Communication:**
- ✅ **Enhanced ping system** with detailed responses
- ✅ **Health check monitoring** every 30 seconds
- ✅ **Better initialization** timing and error handling
- ✅ **Fallback methods** when communication fails

---

## 🚀 **Step-by-Step Fix Process**

### **1. Reload the Extension**
```
1. Go to chrome://extensions/
2. Find "Midjourney Prompt Manager"
3. Click the refresh/reload icon (🔄)
4. This ensures the updated content script is loaded
```

### **2. Refresh Target Websites**
```
1. Go to midjourney.com or discord.com
2. Press Ctrl+F5 (hard refresh) or Cmd+Shift+R (Mac)
3. This clears any cached scripts and loads fresh content
```

### **3. Test Content Script Connection**
```
1. Open the extension popup
2. Click "Test Connection" button
3. Should show: "Content script working on X/Y tabs"
4. If not working, continue to next steps
```

### **4. Manual Content Script Injection**
```
1. Open Midjourney/Discord page
2. Press F12 to open Developer Tools
3. Go to Console tab
4. Paste this code and press Enter:

// Force inject content script
chrome.runtime.sendMessage({action: 'injectContentScript'});

5. Wait 2 seconds, then test extension again
```

---

## 🔍 **Diagnostic Steps**

### **Check 1: Verify Target Websites**
```
✅ Supported URLs:
- https://www.midjourney.com/*
- https://midjourney.com/*
- https://discord.com/*
- https://*.discord.com/*

❌ Not supported:
- Other websites
- Local files (file://)
- Chrome internal pages (chrome://)
```

### **Check 2: Browser Console Inspection**
```
1. Open Midjourney/Discord page
2. Press F12 → Console tab
3. Look for these messages:

✅ Good signs:
"Midjourney Prompt Manager content script starting..."
"PromptSubmitter initialized successfully"
"Content script is active and ready"

❌ Bad signs:
Red error messages
"Content script not responding"
No messages at all
```

### **Check 3: Extension Console**
```
1. Go to chrome://extensions/
2. Click "Details" on the extension
3. Click "Inspect views service worker"
4. Check for error messages in console
```

---

## 🛠️ **Advanced Troubleshooting**

### **If Content Script Still Not Working:**

#### **Method 1: Manual Script Injection**
```javascript
// Run this in the Midjourney/Discord page console (F12)
(function() {
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('content.js');
    document.head.appendChild(script);
    console.log('Content script manually injected');
})();
```

#### **Method 2: Check Permissions**
```
1. Go to chrome://extensions/
2. Click "Details" on the extension
3. Scroll to "Site access"
4. Ensure it's set to "On all sites" or "On specific sites"
5. Add midjourney.com and discord.com if needed
```

#### **Method 3: Reset Extension State**
```javascript
// Run this in extension popup console
chrome.storage.local.clear();
console.log('Extension state cleared');
// Then reload the extension
```

---

## 📊 **What Each Status Message Means**

### **Success Messages:**
```
✅ "Content script working on 1/1 tabs"
   → Content script is loaded and responding

✅ "Content script injected and responding"
   → Script was successfully injected

✅ "Prompt posted automatically"
   → Automatic posting is working
```

### **Warning Messages:**
```
⚠️ "Content script not responding - will use manual mode"
   → Communication failed, using fallback

⚠️ "Content script injected but not responding"
   → Script loaded but not communicating

⚠️ "Try refreshing the Midjourney/Discord page and test again"
   → Page refresh needed
```

### **Error Messages:**
```
❌ "No Midjourney or Discord tabs found"
   → Need to open target websites

❌ "Content script injection failed"
   → Permission or technical issue

❌ "Error testing content script"
   → General communication problem
```

---

## 🎯 **Expected Working Behavior**

### **When Content Script is Working:**
```
1. Click "Test Connection"
2. See: "Content script working on 1/1 tabs"
3. Click "Post Next Prompt"
4. See: "Prompt posted automatically"
5. Prompt appears and submits in Midjourney/Discord
```

### **When Using Fallback Mode:**
```
1. Extension detects communication failure
2. Shows: "Content script not responding - will use manual mode"
3. Automatically switches to direct DOM manipulation
4. Prompts still get posted, just via different method
```

---

## 🔧 **Prevention Tips**

### **To Avoid Content Script Issues:**
1. **Keep extension updated** - reload after any changes
2. **Refresh target pages** - especially after extension updates
3. **Don't modify extension files** - can break content script
4. **Check Chrome version** - ensure compatibility
5. **Monitor console** - watch for error messages

### **Best Practices:**
1. **Test connection first** - before starting batch processing
2. **Use supported URLs** - stick to midjourney.com and discord.com
3. **Allow permissions** - ensure extension has site access
4. **Regular maintenance** - reload extension weekly

---

## ✅ **Success Indicators**

### **Content Script Working:**
- ✅ "Test Connection" shows success
- ✅ Console shows initialization messages
- ✅ Prompts post automatically without manual intervention
- ✅ No "manual mode" warnings

### **Fallback Mode Working:**
- ✅ Extension acknowledges communication failure
- ✅ Switches to manual mode automatically
- ✅ Prompts still get posted (may need manual Enter)
- ✅ Clear status messages explain what's happening

---

## 🆘 **If Nothing Works**

### **Nuclear Reset:**
```
1. Close all browser tabs
2. Go to chrome://extensions/
3. Remove the extension completely
4. Restart Chrome
5. Re-add the extension
6. Test on fresh Midjourney/Discord tabs
```

### **Alternative Browsers:**
```
- Try Chrome Canary
- Try Microsoft Edge (Chromium-based)
- Ensure browser is up to date
```

The enhanced content script system now provides much better communication reliability and comprehensive fallback methods! 🚀
