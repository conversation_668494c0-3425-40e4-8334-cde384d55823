<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background with rounded corners -->
  <rect x="8" y="8" width="112" height="112" rx="20" ry="20" fill="url(#grad1)"/>
  
  <!-- Art palette icon -->
  <g transform="translate(32, 32)">
    <!-- Palette shape -->
    <path d="M32 8C18.7 8 8 18.7 8 32c0 8.8 4.7 16.5 11.7 20.8L24 48c2.2-2.2 5.8-2.2 8 0s2.2 5.8 0 8l-4.8 4.8C30.5 59.3 38.2 64 47 64c13.3 0 24-10.7 24-24S45.3 8 32 8z" fill="white" opacity="0.9"/>
    
    <!-- Paint dots -->
    <circle cx="24" cy="24" r="3" fill="#ff6b6b"/>
    <circle cx="40" cy="20" r="3" fill="#4ecdc4"/>
    <circle cx="44" cy="36" r="3" fill="#45b7d1"/>
    <circle cx="28" cy="40" r="3" fill="#f9ca24"/>
    
    <!-- Brush -->
    <rect x="48" y="16" width="3" height="16" fill="white" opacity="0.8" transform="rotate(45 49.5 24)"/>
    <ellipse cx="52" cy="12" rx="2" ry="4" fill="white" opacity="0.6" transform="rotate(45 52 12)"/>
  </g>
  
  <!-- Text "MJ" for Midjourney -->
  <text x="64" y="100" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white" opacity="0.8">MJ</text>
</svg>
