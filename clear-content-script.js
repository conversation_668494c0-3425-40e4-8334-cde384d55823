// Clear content script state - run this in browser console if needed
// This helps resolve "Identifier already declared" errors

(function() {
    console.log('Clearing Midjourney Prompt Manager content script state...');
    
    // Clear the loaded flag
    if (window.midjourneyPromptManagerLoaded) {
        delete window.midjourneyPromptManagerLoaded;
        console.log('Cleared content script loaded flag');
    }
    
    // Remove any existing message listeners
    if (chrome && chrome.runtime && chrome.runtime.onMessage) {
        try {
            chrome.runtime.onMessage.removeListener();
            console.log('Removed existing message listeners');
        } catch (error) {
            console.log('No message listeners to remove');
        }
    }
    
    console.log('Content script state cleared. You can now reload the extension.');
})();
