# Troubleshooting Guide - Midjourney Prompt Manager v2.0

## 🔧 Common Issues and Solutions

### ❌ "Could not establish connection. Receiving end does not exist."

This error occurs when the popup tries to communicate with the background service worker but it's not running or responding.

#### **✅ Solutions (in order of preference):**

1. **Reload the Extension:**
   - Go to `chrome://extensions/`
   - Find "Midjourney Prompt Manager"
   - Click the refresh/reload icon (🔄)
   - Try opening the popup again

2. **Restart Chrome:**
   - Close Chrome completely
   - Reopen Chrome
   - The extension should work normally

3. **Check Service Worker Status:**
   - Go to `chrome://extensions/`
   - Click "Details" on the extension
   - Look for "Service worker" status
   - If it shows "Inactive", click "Inspect views service worker" to activate it

4. **Use Local Mode (Fallback):**
   - The extension now includes a fallback mode
   - If background script fails, it will switch to local processing
   - You'll see "Background script not available - using local mode"
   - Most features will still work, but batch automation will be limited

#### **🔍 What the Extension Does Now:**
- **Automatic Detection**: Detects when background script is unavailable
- **Graceful Fallback**: Switches to local mode automatically
- **Manual Export/Import**: File operations work without background script
- **Local Batch Tracking**: Basic batch processing in local mode

---

## 🚀 Extension Loading Issues

### ❌ "Failed to load extension"

1. **Check File Structure:**
   ```
   D:\crhomeext10x\
   ├── manifest.json
   ├── popup.html
   ├── popup.js
   ├── content.js
   ├── background.js
   └── (other files...)
   ```

2. **Verify Manifest:**
   - Ensure `manifest.json` is valid JSON
   - Check that all referenced files exist

3. **Enable Developer Mode:**
   - Go to `chrome://extensions/`
   - Toggle "Developer mode" ON
   - Try loading again

---

## 🎯 Functionality Issues

### ❌ Prompts Not Submitting

1. **Check Target Website:**
   - Ensure you're on `midjourney.com` or `discord.com`
   - The input field must be visible and accessible

2. **Test Manual Submission:**
   - Try "Post Next Prompt" button first
   - If manual works, batch processing should work too

3. **Check Browser Console:**
   - Press F12 → Console tab
   - Look for error messages
   - Red errors indicate issues

### ❌ Batch Processing Not Working

1. **Select a Prompt List:**
   - Must select a list from dropdown before starting batch mode
   - Import sample lists if none available

2. **Check Background Script:**
   - If background script fails, extension uses local mode
   - Local mode requires manual prompt submission

3. **Verify Settings:**
   - Check advanced settings for valid timing parameters
   - Ensure min values are less than max values

---

## 📊 Performance Issues

### ❌ Extension Slow or Unresponsive

1. **Clear Extension Data:**
   ```javascript
   // In browser console on extension popup:
   chrome.storage.local.clear()
   ```

2. **Reduce Prompt Lists:**
   - Large prompt lists can slow performance
   - Keep lists under 100 prompts each

3. **Disable Other Extensions:**
   - Temporarily disable other extensions
   - Test if performance improves

---

## 🔍 Debugging Steps

### **1. Check Extension Status**
```
chrome://extensions/ → Midjourney Prompt Manager → Details
```
- Service worker status
- Error messages
- Permissions granted

### **2. Browser Console Logs**
```
F12 → Console → Look for errors
```
- Red errors are critical
- Yellow warnings are informational

### **3. Extension Console**
```
chrome://extensions/ → Details → Inspect views service worker
```
- Background script logs
- Error details
- Performance metrics

### **4. Storage Inspection**
```
F12 → Application → Storage → Extension
```
- Check stored data
- Verify prompt lists
- Review settings

---

## 🆘 Emergency Fixes

### **Complete Reset**
1. Remove extension completely
2. Delete extension folder
3. Re-download/extract files
4. Load extension again

### **Data Backup Before Reset**
1. Export all prompt lists
2. Note custom settings
3. Save to safe location

### **Minimal Installation**
If all else fails, use only core files:
- `manifest.json`
- `popup.html`
- `popup.js`
- `content.js`

---

## 📞 Getting Help

### **Information to Provide:**
1. **Chrome Version**: `chrome://version/`
2. **Extension Version**: 2.0
3. **Error Messages**: Exact text from console
4. **Steps to Reproduce**: What you were doing when error occurred
5. **Operating System**: Windows/Mac/Linux

### **Useful Commands:**
```javascript
// Check extension status
chrome.runtime.getManifest()

// Check storage
chrome.storage.local.get(null, console.log)

// Clear all data
chrome.storage.local.clear()
```

---

## ✅ Prevention Tips

1. **Regular Updates**: Keep Chrome updated
2. **Extension Maintenance**: Reload extension weekly
3. **Data Backup**: Export prompt lists regularly
4. **Monitor Performance**: Watch for slow responses
5. **Clean Installation**: Avoid modifying extension files

---

## 🔄 Version-Specific Notes

### **v2.0 Improvements:**
- ✅ Automatic fallback to local mode
- ✅ Better error handling and recovery
- ✅ Connection status monitoring
- ✅ Graceful degradation of features

### **Known Limitations:**
- Background script may need occasional restart
- Large prompt lists (>100 items) may slow performance
- Some advanced features require background script

The extension is designed to work even when the background script fails, ensuring you can always manage and submit prompts manually.
