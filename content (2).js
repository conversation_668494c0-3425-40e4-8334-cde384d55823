chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'postPrompt') {
        // Find the message input field
        const messageInput = document.querySelector('[role="textbox"]');
        if (messageInput) {
            // Focus the input field
            messageInput.focus();
            
            // Set the prompt text
            messageInput.textContent = request.prompt;
            
            // Trigger input event to ensure Discord recognizes the change
            messageInput.dispatchEvent(new Event('input', { bubbles: true }));
            
            // Find and click the submit button
            setTimeout(() => {
                const buttons = document.querySelectorAll('button');
                for (const button of buttons) {
                    if (button.textContent.includes('Send')) {
                        button.click();
                        break;
                    }
                }
            }, 500);
        }
    }
});
