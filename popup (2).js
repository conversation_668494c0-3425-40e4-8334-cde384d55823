document.addEventListener('DOMContentLoaded', function() {
    // Load saved prompts when popup opens
    chrome.storage.local.get(['prompts', 'currentIndex'], function(result) {
        if (result.prompts) {
            document.getElementById('promptList').value = result.prompts.join('\n');
        }
    });

    // Save prompts
    document.getElementById('savePrompts').addEventListener('click', function() {
        const promptText = document.getElementById('promptList').value;
        const prompts = promptText.split('\n').filter(prompt => prompt.trim() !== '');
        
        chrome.storage.local.set({
            prompts: prompts,
            currentIndex: 0
        }, function() {
            alert('Prompts saved!');
        });
    });

    // Post prompt
    document.getElementById('postPrompt').addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs[0].url.includes('discord.com')) {
                chrome.storage.local.get(['prompts', 'currentIndex'], function(result) {
                    if (result.prompts && result.prompts.length > 0) {
                        const currentIndex = result.currentIndex || 0;
                        const prompt = result.prompts[currentIndex];
                        
                        // Send message to content script
                        chrome.tabs.sendMessage(tabs[0].id, {
                            action: 'postPrompt',
                            prompt: '/imagine ' + prompt
                        });

                        // Update index
                        const nextIndex = (currentIndex + 1) % result.prompts.length;
                        chrome.storage.local.set({ currentIndex: nextIndex });
                    } else {
                        alert('No prompts saved! Please add and save some prompts first.');
                    }
                });
            } else {
                alert('Please navigate to Discord to post prompts!');
            }
        });
    });
});
