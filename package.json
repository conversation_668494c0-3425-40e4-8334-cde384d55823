{"name": "midjourney-prompt-manager", "version": "2.0.0", "description": "Advanced Chrome extension for batch management and automated submission of prompts to Midjourney and Discord", "main": "manifest.json", "scripts": {"build": "echo 'Extension is ready for installation'", "test": "echo 'Open test-extension.html in browser for testing'", "validate": "echo 'Check manifest.json and all required files'", "package": "echo 'Zip all files for distribution'"}, "keywords": ["midjourney", "discord", "ai", "automation", "prompt", "batch", "chrome-extension", "productivity"], "author": "Midjourney Prompt Manager Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/midjourney-prompt-manager"}, "bugs": {"url": "https://github.com/your-username/midjourney-prompt-manager/issues"}, "homepage": "https://github.com/your-username/midjourney-prompt-manager#readme", "engines": {"chrome": ">=88.0.0"}, "extensionInfo": {"manifestVersion": 3, "permissions": ["storage", "activeTab", "scripting", "downloads", "unlimitedStorage", "notifications"], "hostPermissions": ["*://www.midjourney.com/*", "*://discord.com/*"], "contentScripts": [{"matches": ["*://www.midjourney.com/*", "*://discord.com/*"], "js": ["content.js"]}]}, "files": ["manifest.json", "popup.html", "popup.js", "content.js", "background.js", "README.md", "INSTALLATION.md", "CHANGELOG.md", "test-extension.html", "create-icons.html", "sample-prompts-*.json"], "devDependencies": {}, "dependencies": {}, "installationRequirements": {"browser": "Google Chrome 88+", "storage": "50MB minimum", "permissions": "Developer mode enabled"}, "features": {"batchProcessing": {"description": "Automated batch submission with randomized timing", "batchSize": "7-10 prompts", "interBatchDelay": "16-40 minutes", "intraBatchDelay": "1-10 seconds"}, "promptManagement": {"description": "Complete prompt list management system", "features": ["save", "load", "import", "export", "thumbnails", "metadata"]}, "userInterface": {"description": "Modern, responsive user interface", "features": ["modal dialogs", "real-time status", "progress tracking"]}, "compatibility": {"description": "Cross-browser compatibility and error handling", "platforms": ["Midjourney.com", "Discord.com"], "browsers": ["Chrome 88+", "Edge 88+"]}}, "configuration": {"defaultSettings": {"minBatchSize": 7, "maxBatchSize": 10, "minBatchDelay": 16, "maxBatchDelay": 40, "minPromptDelay": 1, "maxPromptDelay": 10, "enableLogging": false, "enableNotifications": true}, "storageKeys": ["promptLists", "advancedSettings", "batchProcessing", "errorLog", "loopPost"]}, "testing": {"testFile": "test-extension.html", "requirements": ["Active Midjourney or Discord tab", "Extension loaded in developer mode", "Sample prompt lists imported"], "testCases": ["Basic prompt submission", "Batch processing functionality", "Prompt list management", "Settings configuration", "Error handling"]}, "support": {"documentation": "README.md", "installation": "INSTALLATION.md", "changelog": "CHANGELOG.md", "testing": "test-extension.html"}}