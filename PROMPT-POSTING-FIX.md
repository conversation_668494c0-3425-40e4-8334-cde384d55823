# Prompt Posting Fix Guide

## 🔧 **Issue: "Could not establish connection" when posting prompts**

This error occurs when the popup can't communicate with the content script on the Midjourney/Discord page.

---

## ✅ **Step-by-Step Fix**

### **1. Reload the Extension**
```
1. Go to chrome://extensions/
2. Find "Midjourney Prompt Manager"
3. Click the refresh/reload icon (🔄)
4. Close and reopen the extension popup
```

### **2. Test the Connection**
```
1. Navigate to midjourney.com or discord.com
2. Open the extension popup
3. Click "🔍 Test Connection" button
4. Check the status message
```

### **3. Manual Content Script Injection**
If the test fails, the extension will automatically inject the content script when you try to post a prompt.

### **4. Use the Test Page**
```
1. Open test-page.html in your browser
2. Test the extension functionality
3. Verify prompt insertion works
```

---

## 🎯 **How the Fixed Extension Works**

### **Enhanced Prompt Posting Process:**

1. **Automatic Script Injection**: Extension automatically injects content script if needed
2. **Fallback Method**: If content script fails, uses direct DOM manipulation
3. **Manual Insertion**: As last resort, inserts prompt text and asks user to press Enter

### **What You'll See:**

- ✅ **"Posting prompt X/Y..."** - Normal operation
- ⚠️ **"Using manual posting method..."** - Fallback mode
- ✅ **"Prompt inserted - please press Enter to submit"** - Manual mode

---

## 🔍 **Testing Steps**

### **Test on Midjourney.com:**
```
1. Go to https://www.midjourney.com/
2. Open extension popup
3. Enter test prompt: "a beautiful sunset --ar 16:9"
4. Click "📤 Post Next Prompt"
5. Verify prompt appears in input field
```

### **Test on Discord.com:**
```
1. Go to https://discord.com/
2. Open a channel with Midjourney bot
3. Open extension popup
4. Enter test prompt: "/imagine a beautiful sunset --ar 16:9"
5. Click "📤 Post Next Prompt"
6. Verify prompt appears in message input
```

### **Test with Test Page:**
```
1. Open test-page.html in browser
2. Open extension popup
3. Click "🔍 Test Connection"
4. Try posting a prompt
5. Check if prompt appears in test input fields
```

---

## 🛠️ **Troubleshooting**

### **If "Test Connection" Fails:**
- ✅ Extension will use fallback methods automatically
- ✅ Prompt posting will still work, just with manual submission
- ✅ You'll see "Content script not responding - will use manual mode"

### **If Prompt Doesn't Appear:**
1. **Check the website**: Must be on midjourney.com or discord.com
2. **Refresh the page**: Sometimes input fields change
3. **Check browser console**: Press F12 → Console for errors
4. **Try manual mode**: Extension will fall back automatically

### **If Nothing Works:**
1. **Restart Chrome**: Close completely and reopen
2. **Reload extension**: Go to chrome://extensions/ and reload
3. **Check permissions**: Ensure extension has access to the websites

---

## 📋 **What's Different Now**

### **Before (v1.0):**
- ❌ Single point of failure
- ❌ No fallback methods
- ❌ Hard to debug issues

### **After (v2.0 Fixed):**
- ✅ **Triple fallback system**:
  1. Content script communication
  2. Direct script injection
  3. Manual DOM manipulation
- ✅ **Automatic error recovery**
- ✅ **Clear status messages**
- ✅ **Test connection feature**

---

## 🎯 **Expected Behavior**

### **Successful Posting:**
```
1. "Preparing to post prompt..." (blue)
2. "Posting prompt 1/5..." (blue)
3. "Posted prompt 1/5" (green)
```

### **Fallback Mode:**
```
1. "Preparing to post prompt..." (blue)
2. "Using manual posting method..." (yellow)
3. "Prompt inserted - please press Enter to submit" (green)
```

### **Connection Issues:**
```
1. "Testing content script connection..." (blue)
2. "Content script not responding - will use manual mode" (yellow)
3. Prompt posting still works with manual submission
```

---

## 🚀 **Quick Test Commands**

### **Browser Console Test:**
```javascript
// Test if extension is loaded
chrome.runtime.getManifest()

// Test content script injection
chrome.scripting.executeScript({
  target: { tabId: (await chrome.tabs.query({active: true}))[0].id },
  files: ['content.js']
})
```

### **Extension Popup Console:**
```javascript
// Test storage
chrome.storage.local.get(null, console.log)

// Test tab access
chrome.tabs.query({active: true, currentWindow: true}, console.log)
```

---

## ✅ **Success Indicators**

- Extension popup opens without errors
- "🔍 Test Connection" shows success or graceful fallback
- Prompts appear in input fields (even if manual submission required)
- Status messages are clear and helpful
- No red error messages in browser console

---

## 📞 **Still Having Issues?**

If the extension still doesn't work after following this guide:

1. **Check Chrome version**: Must be 88 or newer
2. **Try incognito mode**: Test without other extensions
3. **Check website changes**: Midjourney/Discord may have updated their interface
4. **Use test page**: Verify basic functionality works

The extension now has multiple fallback methods, so it should work even if the primary method fails. The worst case is that you'll need to press Enter manually after the prompt is inserted.
