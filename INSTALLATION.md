# Installation Guide - Midjourney Prompt Manager v2.0

## 📦 Package Contents

Your extension package should contain the following files:

### Core Extension Files
- `manifest.json` - Extension configuration
- `popup.html` - Main user interface
- `popup.js` - UI logic and functionality
- `content.js` - Page interaction script
- `background.js` - Background service worker

### Documentation
- `README.md` - Complete documentation
- `INSTALLATION.md` - This installation guide
- `test-extension.html` - Testing and validation guide

### Sample Content
- `sample-prompts-artistic.json` - Artistic style prompts
- `sample-prompts-photography.json` - Photography prompts
- `sample-prompts-fantasy.json` - Fantasy and sci-fi prompts
- `sample-prompts-business.json` - Business and marketing prompts

### Utilities
- `create-icons.html` - Icon generator utility

## 🚀 Installation Steps

### Method 1: Developer Mode (Recommended)

1. **Download the Extension**
   - Download all files to a folder on your computer
   - Keep all files in the same directory

2. **Open Chrome Extensions**
   - Open Google Chrome
   - Navigate to `chrome://extensions/`
   - Or go to Menu → More Tools → Extensions

3. **Enable Developer Mode**
   - Toggle "Developer mode" in the top right corner
   - This enables the "Load unpacked" option

4. **Load the Extension**
   - Click "Load unpacked"
   - Select the folder containing the extension files
   - The extension should appear in your extensions list

5. **Pin the Extension**
   - Click the puzzle piece icon in the toolbar
   - Find "Midjourney Prompt Manager"
   - Click the pin icon to keep it visible

### Method 2: Chrome Web Store (Future)
*This extension is not yet published to the Chrome Web Store*

## 🔧 Initial Setup

### 1. Create Icons (Optional)
If you don't have icon files, use the icon generator:
1. Open `create-icons.html` in your browser
2. Download the generated icon files
3. Place them in the extension folder

### 2. Import Sample Prompt Lists
1. Click the extension icon
2. Click "Import List" 
3. Select one of the sample JSON files
4. The prompts will be loaded and ready to use

### 3. Configure Settings
1. Open the extension popup
2. Click "Configure" next to Advanced Settings
3. Adjust timing parameters as needed
4. Enable logging if you want detailed operation tracking

## ✅ Verification

### Check Installation Success
- Extension icon appears in toolbar
- Popup opens when clicked
- No error messages in Chrome console
- Sample prompts can be imported

### Test Basic Functionality
1. Navigate to midjourney.com or discord.com
2. Open the extension popup
3. Enter a test prompt
4. Click "Post Next Prompt"
5. Verify the prompt appears in the input field

## 🔒 Permissions Explained

The extension requires these permissions:

- **Storage**: Save your prompt lists and settings locally
- **Active Tab**: Interact with the current webpage
- **Scripting**: Inject scripts to submit prompts
- **Downloads**: Export prompt lists as files
- **Unlimited Storage**: Store large prompt collections
- **Notifications**: Show batch completion alerts

## 🐛 Troubleshooting

### Extension Won't Load
- Check that all files are in the same folder
- Ensure `manifest.json` is present and valid
- Try refreshing the extensions page
- Check Chrome console for error messages

### Prompts Not Submitting
- Verify you're on midjourney.com or discord.com
- Check that the input field is visible
- Try refreshing the webpage
- Enable logging to see detailed error messages

### Performance Issues
- Close unused tabs to free memory
- Disable other extensions temporarily
- Check Chrome task manager for resource usage

## 🔄 Updates

### Manual Updates
1. Download the new version files
2. Replace the old files in your extension folder
3. Go to `chrome://extensions/`
4. Click the refresh icon for the extension

### Backup Your Data
Before updating:
1. Export all your prompt lists
2. Note your custom settings
3. Keep backup files safe

## 🆘 Support

### Getting Help
1. Check the troubleshooting section
2. Review error logs in the extension
3. Open browser developer console (F12)
4. Check the GitHub repository for issues

### Reporting Issues
When reporting problems, include:
- Chrome version
- Extension version
- Error messages
- Steps to reproduce
- Screenshots if helpful

## 📋 System Requirements

### Minimum Requirements
- Google Chrome 88+ (Manifest V3 support)
- 50MB free disk space
- Active internet connection for target websites

### Recommended
- Google Chrome 100+
- 100MB free disk space
- 4GB RAM for optimal performance

## 🔐 Privacy & Security

### Data Storage
- All data stored locally in Chrome
- No external servers or cloud storage
- Prompt lists remain on your computer

### Network Access
- Only accesses midjourney.com and discord.com
- No data transmitted to third parties
- No tracking or analytics

## 📝 License

This extension is provided under the MIT License. See the LICENSE file for details.

## 🎯 Next Steps

After installation:
1. Import sample prompt lists
2. Create your own prompt collections
3. Configure batch processing settings
4. Start using automated prompt submission
5. Export your lists for backup

Enjoy using the Midjourney Prompt Manager v2.0!
